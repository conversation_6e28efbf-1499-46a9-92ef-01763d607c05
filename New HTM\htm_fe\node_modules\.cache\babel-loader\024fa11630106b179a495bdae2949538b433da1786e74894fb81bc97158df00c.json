{"ast": null, "code": "var _FirebaseRoomListener;\nimport { ref, onValue, onDisconnect, set, get, remove, serverTimestamp } from \"firebase/database\";\nimport { database } from \"../firebase-config\";\nexport class FirebaseRoomListener {\n  constructor(roomId) {\n    this.roomId = void 0;\n    this.roomId = roomId;\n  }\n  static getInstance(roomId) {\n    if (!this.instances.has(roomId)) {\n      this.instances.set(roomId, new FirebaseRoomListener(roomId));\n    }\n    return this.instances.get(roomId);\n  }\n  listen(child, callback, skipFirst = false) {\n    const fullRef = ref(database, `rooms/${this.roomId}/${child}`);\n    let isFirst = true;\n    const unsubscribe = onValue(fullRef, snapshot => {\n      if (skipFirst && isFirst) {\n        isFirst = false;\n        return;\n      }\n      if (!snapshot.exists()) return;\n      const data = snapshot.val();\n      callback(data);\n    });\n    return unsubscribe;\n  }\n  listenToPlayers(callback) {\n    return this.listen(\"players\", callback);\n  }\n  listenToQuestion(callback) {\n    return this.listen(\"question\", callback);\n  }\n  listenToSelectedCell(callback) {\n    return this.listen(\"cell\", callback);\n  }\n  listenToCellColor(callback) {\n    return this.listen(\"color\", callback);\n  }\n  listenToCurrentQuestionsNumber(callback) {\n    return this.listen(\"currentQuestions\", callback);\n  }\n  listenToBuzzing(callback) {\n    return this.listen(\"buzzedPlayer\", callback);\n  }\n  listenToStar(callback) {\n    return this.listen(\"star\", callback);\n  }\n  listenToObstacle(callback) {\n    return this.listen(\"obstacles\", callback);\n  }\n  listenToPackets(callback) {\n    return this.listen(\"packets\", callback);\n  }\n  listenToSelectedPacket(callback) {\n    return this.listen(\"selectedPacket\", callback);\n  }\n  listenToCurrentTurn(callback) {\n    return this.listen(\"turn\", callback);\n  }\n  listenToCorrectAnswer(callback) {\n    return this.listen(\"answers\", callback);\n  }\n  listenToSound(callback) {\n    return this.listen(\"sound\", callback, true);\n  }\n  listenToOpenBuzz(callback) {\n    return this.listen(\"openBuzzed\", callback);\n  }\n  listenToTimeStart(callback) {\n    const refPath = ref(database, `rooms/${this.roomId}/times`);\n    let isFirstCall = true;\n    let lastStartTime = Number(localStorage.getItem(\"lastStartTime\")) || 0;\n    const unsubscribe = onValue(refPath, snapshot => {\n      const time = snapshot.val();\n      if (isFirstCall) {\n        isFirstCall = false;\n        return;\n      }\n      if (time && time !== lastStartTime) {\n        lastStartTime = time;\n        localStorage.setItem(\"lastStartTime\", time.toString());\n        if (callback) {\n          callback();\n        }\n      }\n    });\n    return unsubscribe;\n  }\n  listenToScores(callback) {\n    return this.listen(\"scores\", callback);\n  }\n  listenToHistory(callback) {\n    return this.listen(\"round_scores\", callback);\n  }\n  listenToGrid(callback) {\n    return this.listen(\"grid\", callback);\n  }\n  listenToRoundStart(callback) {\n    return this.listen(\"rounds\", callback);\n  }\n  listenToSelectRow(callback) {\n    return this.listen(\"select\", callback);\n  }\n  listenToIncorrectRow(callback) {\n    return this.listen(\"incorrect\", callback);\n  }\n  listenToCorrectRow(callback) {\n    return this.listen(\"correct\", callback);\n  }\n  listenToBroadcastedAnswer(callback) {\n    return this.listen(\"answerLists\", callback);\n  }\n  listenToSpectatorJoin(callback) {\n    const path = ref(database, `rooms/${this.roomId}/spectators`);\n    return onValue(path, snapshot => {\n      callback(snapshot.size);\n    });\n  }\n  listenToRules(callback) {\n    return this.listen(\"rules\", callback);\n  }\n  listenToRoundRules(callback) {\n    return this.listen(\"showRules\", callback);\n  }\n  listenToGridActions(callback) {\n    return this.listen(\"rounds\", callback);\n  }\n  listenToUsedTopics(callback) {\n    return this.listen(\"usedTopics\", callback);\n  }\n  listenToReturnToTopicSelection(callback) {\n    return this.listen(\"returnToTopicSelection\", callback);\n  }\n  async setUsedTopic(topic) {\n    const usedRef = ref(database, `rooms/${this.roomId}/usedTopics`);\n    const snap = await get(usedRef);\n    const current = snap.val() || [];\n    if (!current.includes(topic)) {\n      await set(usedRef, [...current, topic]);\n    }\n  }\n  async setReturnToTopicSelection(value) {\n    const refPath = ref(database, `rooms/${this.roomId}/returnToTopicSelection`);\n    await set(refPath, value);\n  }\n  async setupDisconnect(path, data) {\n    const userRef = ref(database, `rooms/${this.roomId}/${path}`);\n    const disconnectHandler = onDisconnect(userRef);\n    await disconnectHandler.remove();\n    const interval = setInterval(() => {\n      set(userRef, {\n        ...data,\n        lastActive: serverTimestamp()\n      });\n    }, 5000);\n    return () => {\n      clearInterval(interval);\n      disconnectHandler.cancel();\n    };\n  }\n  async deletePath(path) {\n    const refPath = ref(database, `rooms/${this.roomId}/${path}`);\n    await remove(refPath);\n  }\n  async addPlayer(uid, data) {\n    const pathRef = ref(database, `rooms/${this.roomId}/players/${uid}`);\n    await set(pathRef, {\n      joined_at: Date.now(),\n      data\n    });\n  }\n}\n_FirebaseRoomListener = FirebaseRoomListener;\nFirebaseRoomListener.instances = new Map();", "map": {"version": 3, "names": ["ref", "onValue", "onDisconnect", "set", "get", "remove", "serverTimestamp", "database", "FirebaseRoomListener", "constructor", "roomId", "getInstance", "instances", "has", "listen", "child", "callback", "<PERSON><PERSON><PERSON><PERSON>", "fullRef", "<PERSON><PERSON><PERSON><PERSON>", "unsubscribe", "snapshot", "exists", "data", "val", "listenToPlayers", "listenToQuestion", "listenToSelectedCell", "listenToCellColor", "listenToCurrentQuestionsNumber", "listenToBuzzing", "listenToStar", "listenToObstacle", "listenToPackets", "listenToSelectedPacket", "listenToCurrentTurn", "listenToCorrectAnswer", "listenToSound", "listenToOpenBuzz", "listenToTimeStart", "refPath", "isFirstCall", "lastStartTime", "Number", "localStorage", "getItem", "time", "setItem", "toString", "listenToScores", "listenToHistory", "listenToGrid", "listenToRoundStart", "listenToSelectRow", "listenToIncorrectRow", "listenToCorrectRow", "listenToBroadcastedAnswer", "listenToSpectatorJoin", "path", "size", "listenToRules", "listenToRoundRules", "listenToGridActions", "listenToUsedTopics", "listenToReturnToTopicSelection", "setUsedTopic", "topic", "usedRef", "snap", "current", "includes", "setReturnToTopicSelection", "value", "setupDisconnect", "userRef", "disconnect<PERSON><PERSON><PERSON>", "interval", "setInterval", "lastActive", "clearInterval", "cancel", "deletePath", "addPlayer", "uid", "pathRef", "joined_at", "Date", "now", "_FirebaseRoomListener", "Map"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/services/firebaseServices.ts"], "sourcesContent": ["import { ref, onValue, Unsubscribe, onDisconnect, set, get, remove, serverTimestamp } from \"firebase/database\";\r\nimport { database } from \"../firebase-config\";\r\nimport { Answer, Score, User } from \"../type\";\r\n\r\nexport class FirebaseRoomListener {\r\n  private static instances: Map<string, FirebaseRoomListener> = new Map();\r\n  private roomId: string;\r\n\r\n  constructor(roomId: string) {\r\n    this.roomId = roomId;\r\n  }\r\n\r\n  static getInstance(roomId: string): FirebaseRoomListener {\r\n    if (!this.instances.has(roomId)) {\r\n      this.instances.set(roomId, new FirebaseRoomListener(roomId));\r\n    }\r\n    return this.instances.get(roomId)!;\r\n  }\r\n\r\n  private listen<T = any>(child: string, callback: (data: T) => void, skipFirst = false): Unsubscribe {\r\n    const fullRef = ref(database, `rooms/${this.roomId}/${child}`);\r\n    let isFirst = true;\r\n    const unsubscribe: Unsubscribe = onValue(fullRef, (snapshot) => {\r\n      if (skipFirst && isFirst) {\r\n        isFirst = false;\r\n        return;\r\n      }\r\n      if (!snapshot.exists()) return;\r\n      const data: T = snapshot.val();\r\n      callback(data);\r\n    });\r\n    return unsubscribe;\r\n  }\r\n\r\n  listenToPlayers(callback: (data: User) => void) {\r\n    return this.listen(\"players\", callback);\r\n  }\r\n\r\n  listenToQuestion(callback: (data: any) => void) {\r\n    return this.listen(\"question\", callback);\r\n  }\r\n\r\n  listenToSelectedCell(callback: (data: any) => void) {\r\n    return this.listen(\"cell\", callback);\r\n  }\r\n\r\n  listenToCellColor(callback: (data: any) => void) {\r\n    return this.listen(\"color\", callback);\r\n  }\r\n\r\n  listenToCurrentQuestionsNumber(callback: (data: number) => void) {\r\n    return this.listen(\"currentQuestions\", callback);\r\n  }\r\n\r\n  listenToBuzzing(callback: (data: string) => void) {\r\n    return this.listen(\"buzzedPlayer\", callback);\r\n  }\r\n\r\n  listenToStar(callback: (data: string) => void) {\r\n    return this.listen(\"star\", callback);\r\n  }\r\n\r\n  listenToObstacle(callback: (data: any) => void) {\r\n    return this.listen(\"obstacles\", callback);\r\n  }\r\n\r\n  listenToPackets(callback: (data: string[]) => void) {\r\n    return this.listen(\"packets\", callback);\r\n  }\r\n\r\n  listenToSelectedPacket(callback: (data: string) => void) {\r\n    return this.listen(\"selectedPacket\", callback);\r\n  }\r\n\r\n  listenToCurrentTurn(callback: (data: number) => void) {\r\n    return this.listen(\"turn\", callback);\r\n  }\r\n\r\n  listenToCorrectAnswer(callback: (data: string) => void) {\r\n    return this.listen(\"answers\", callback);\r\n  }\r\n\r\n  listenToSound(callback: (data: string) => void) {\r\n    return this.listen(\"sound\", callback, true);\r\n  }\r\n\r\n  listenToOpenBuzz(callback: (data: string) => void) {\r\n    return this.listen(\"openBuzzed\", callback);\r\n  }\r\n\r\n  listenToTimeStart(callback?: () => void): Unsubscribe {\r\n    const refPath = ref(database, `rooms/${this.roomId}/times`);\r\n    let isFirstCall = true;\r\n    let lastStartTime = Number(localStorage.getItem(\"lastStartTime\")) || 0;\r\n\r\n    const unsubscribe: Unsubscribe = onValue(refPath, (snapshot) => {\r\n      const time = snapshot.val();\r\n      if (isFirstCall) {\r\n        isFirstCall = false;\r\n        return;\r\n      }\r\n      if (time && time !== lastStartTime) {\r\n        lastStartTime = time;\r\n        localStorage.setItem(\"lastStartTime\", time.toString());\r\n        if(callback) {\r\n          callback();\r\n        }\r\n      }\r\n    });\r\n    return unsubscribe;\r\n  }\r\n\r\n  listenToScores(callback: (data: Score[]) => void) {\r\n    return this.listen(\"scores\", callback);\r\n  }\r\n\r\n  listenToHistory(callback: (data: any) => void) {\r\n    return this.listen(\"round_scores\", callback);\r\n  }\r\n\r\n  listenToGrid(callback: (data: string[][]) => void) {\r\n    return this.listen(\"grid\", callback);\r\n  }\r\n\r\n  listenToRoundStart(callback: (data: any) => void) {\r\n    return this.listen(\"rounds\", callback);\r\n  }\r\n\r\n  listenToSelectRow(callback: (data: any) => void) {\r\n    return this.listen(\"select\", callback);\r\n  }\r\n\r\n  listenToIncorrectRow(callback: (data: any) => void) {\r\n    return this.listen(\"incorrect\", callback);\r\n  }\r\n\r\n  listenToCorrectRow(callback: (data: any) => void) {\r\n    return this.listen(\"correct\", callback);\r\n  }\r\n\r\n  listenToBroadcastedAnswer(callback: (data: Answer[]) => void) {\r\n    return this.listen(\"answerLists\", callback);\r\n  }\r\n\r\n  listenToSpectatorJoin(callback: (count: number) => void): Unsubscribe {\r\n    const path = ref(database, `rooms/${this.roomId}/spectators`);\r\n    return onValue(path, (snapshot) => {\r\n      callback(snapshot.size);\r\n    });\r\n  }\r\n\r\n  listenToRules(callback: (data: any) => void) {\r\n    return this.listen(\"rules\", callback);\r\n  }\r\n\r\n  listenToRoundRules(callback: (data: any) => void) {\r\n    return this.listen(\"showRules\", callback);\r\n  }\r\n\r\n  listenToGridActions(callback: (data: any) => void) {\r\n    return this.listen(\"rounds\", callback);\r\n  }\r\n\r\n  listenToUsedTopics(callback: (topics: string[]) => void) {\r\n    return this.listen(\"usedTopics\", callback);\r\n  }\r\n\r\n  listenToReturnToTopicSelection(callback: (shouldReturn: boolean) => void) {\r\n    return this.listen(\"returnToTopicSelection\", callback);\r\n  }\r\n\r\n  async setUsedTopic(topic: string): Promise<void> {\r\n    const usedRef = ref(database, `rooms/${this.roomId}/usedTopics`);\r\n    const snap = await get(usedRef);\r\n    const current = snap.val() || [];\r\n    if (!current.includes(topic)) {\r\n      await set(usedRef, [...current, topic]);\r\n    }\r\n  }\r\n\r\n  async setReturnToTopicSelection(value: boolean) {\r\n    const refPath = ref(database, `rooms/${this.roomId}/returnToTopicSelection`);\r\n    await set(refPath, value);\r\n  }\r\n\r\n  async setupDisconnect(path: string, data: any): Promise<() => void> {\r\n    const userRef = ref(database, `rooms/${this.roomId}/${path}`);\r\n    const disconnectHandler = onDisconnect(userRef);\r\n    await disconnectHandler.remove();\r\n\r\n    const interval = setInterval(() => {\r\n      set(userRef, { ...data, lastActive: serverTimestamp() });\r\n    }, 5000);\r\n\r\n    return () => {\r\n      clearInterval(interval);\r\n      disconnectHandler.cancel();\r\n    };\r\n  }\r\n\r\n  async deletePath(path: string): Promise<void> {\r\n    const refPath = ref(database, `rooms/${this.roomId}/${path}`);\r\n    await remove(refPath);\r\n  }\r\n\r\n  async addPlayer(uid: string, data: any) {\r\n    const pathRef = ref(database, `rooms/${this.roomId}/players/${uid}`);\r\n    await set(pathRef, {\r\n      joined_at: Date.now(),\r\n      data,\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,GAAG,EAAEC,OAAO,EAAeC,YAAY,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,eAAe,QAAQ,mBAAmB;AAC9G,SAASC,QAAQ,QAAQ,oBAAoB;AAG7C,OAAO,MAAMC,oBAAoB,CAAC;EAIhCC,WAAWA,CAACC,MAAc,EAAE;IAAA,KAFpBA,MAAM;IAGZ,IAAI,CAACA,MAAM,GAAGA,MAAM;EACtB;EAEA,OAAOC,WAAWA,CAACD,MAAc,EAAwB;IACvD,IAAI,CAAC,IAAI,CAACE,SAAS,CAACC,GAAG,CAACH,MAAM,CAAC,EAAE;MAC/B,IAAI,CAACE,SAAS,CAACT,GAAG,CAACO,MAAM,EAAE,IAAIF,oBAAoB,CAACE,MAAM,CAAC,CAAC;IAC9D;IACA,OAAO,IAAI,CAACE,SAAS,CAACR,GAAG,CAACM,MAAM,CAAC;EACnC;EAEQI,MAAMA,CAAUC,KAAa,EAAEC,QAA2B,EAAEC,SAAS,GAAG,KAAK,EAAe;IAClG,MAAMC,OAAO,GAAGlB,GAAG,CAACO,QAAQ,EAAE,SAAS,IAAI,CAACG,MAAM,IAAIK,KAAK,EAAE,CAAC;IAC9D,IAAII,OAAO,GAAG,IAAI;IAClB,MAAMC,WAAwB,GAAGnB,OAAO,CAACiB,OAAO,EAAGG,QAAQ,IAAK;MAC9D,IAAIJ,SAAS,IAAIE,OAAO,EAAE;QACxBA,OAAO,GAAG,KAAK;QACf;MACF;MACA,IAAI,CAACE,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAE;MACxB,MAAMC,IAAO,GAAGF,QAAQ,CAACG,GAAG,CAAC,CAAC;MAC9BR,QAAQ,CAACO,IAAI,CAAC;IAChB,CAAC,CAAC;IACF,OAAOH,WAAW;EACpB;EAEAK,eAAeA,CAACT,QAA8B,EAAE;IAC9C,OAAO,IAAI,CAACF,MAAM,CAAC,SAAS,EAAEE,QAAQ,CAAC;EACzC;EAEAU,gBAAgBA,CAACV,QAA6B,EAAE;IAC9C,OAAO,IAAI,CAACF,MAAM,CAAC,UAAU,EAAEE,QAAQ,CAAC;EAC1C;EAEAW,oBAAoBA,CAACX,QAA6B,EAAE;IAClD,OAAO,IAAI,CAACF,MAAM,CAAC,MAAM,EAAEE,QAAQ,CAAC;EACtC;EAEAY,iBAAiBA,CAACZ,QAA6B,EAAE;IAC/C,OAAO,IAAI,CAACF,MAAM,CAAC,OAAO,EAAEE,QAAQ,CAAC;EACvC;EAEAa,8BAA8BA,CAACb,QAAgC,EAAE;IAC/D,OAAO,IAAI,CAACF,MAAM,CAAC,kBAAkB,EAAEE,QAAQ,CAAC;EAClD;EAEAc,eAAeA,CAACd,QAAgC,EAAE;IAChD,OAAO,IAAI,CAACF,MAAM,CAAC,cAAc,EAAEE,QAAQ,CAAC;EAC9C;EAEAe,YAAYA,CAACf,QAAgC,EAAE;IAC7C,OAAO,IAAI,CAACF,MAAM,CAAC,MAAM,EAAEE,QAAQ,CAAC;EACtC;EAEAgB,gBAAgBA,CAAChB,QAA6B,EAAE;IAC9C,OAAO,IAAI,CAACF,MAAM,CAAC,WAAW,EAAEE,QAAQ,CAAC;EAC3C;EAEAiB,eAAeA,CAACjB,QAAkC,EAAE;IAClD,OAAO,IAAI,CAACF,MAAM,CAAC,SAAS,EAAEE,QAAQ,CAAC;EACzC;EAEAkB,sBAAsBA,CAAClB,QAAgC,EAAE;IACvD,OAAO,IAAI,CAACF,MAAM,CAAC,gBAAgB,EAAEE,QAAQ,CAAC;EAChD;EAEAmB,mBAAmBA,CAACnB,QAAgC,EAAE;IACpD,OAAO,IAAI,CAACF,MAAM,CAAC,MAAM,EAAEE,QAAQ,CAAC;EACtC;EAEAoB,qBAAqBA,CAACpB,QAAgC,EAAE;IACtD,OAAO,IAAI,CAACF,MAAM,CAAC,SAAS,EAAEE,QAAQ,CAAC;EACzC;EAEAqB,aAAaA,CAACrB,QAAgC,EAAE;IAC9C,OAAO,IAAI,CAACF,MAAM,CAAC,OAAO,EAAEE,QAAQ,EAAE,IAAI,CAAC;EAC7C;EAEAsB,gBAAgBA,CAACtB,QAAgC,EAAE;IACjD,OAAO,IAAI,CAACF,MAAM,CAAC,YAAY,EAAEE,QAAQ,CAAC;EAC5C;EAEAuB,iBAAiBA,CAACvB,QAAqB,EAAe;IACpD,MAAMwB,OAAO,GAAGxC,GAAG,CAACO,QAAQ,EAAE,SAAS,IAAI,CAACG,MAAM,QAAQ,CAAC;IAC3D,IAAI+B,WAAW,GAAG,IAAI;IACtB,IAAIC,aAAa,GAAGC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC;IAEtE,MAAMzB,WAAwB,GAAGnB,OAAO,CAACuC,OAAO,EAAGnB,QAAQ,IAAK;MAC9D,MAAMyB,IAAI,GAAGzB,QAAQ,CAACG,GAAG,CAAC,CAAC;MAC3B,IAAIiB,WAAW,EAAE;QACfA,WAAW,GAAG,KAAK;QACnB;MACF;MACA,IAAIK,IAAI,IAAIA,IAAI,KAAKJ,aAAa,EAAE;QAClCA,aAAa,GAAGI,IAAI;QACpBF,YAAY,CAACG,OAAO,CAAC,eAAe,EAAED,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC;QACtD,IAAGhC,QAAQ,EAAE;UACXA,QAAQ,CAAC,CAAC;QACZ;MACF;IACF,CAAC,CAAC;IACF,OAAOI,WAAW;EACpB;EAEA6B,cAAcA,CAACjC,QAAiC,EAAE;IAChD,OAAO,IAAI,CAACF,MAAM,CAAC,QAAQ,EAAEE,QAAQ,CAAC;EACxC;EAEAkC,eAAeA,CAAClC,QAA6B,EAAE;IAC7C,OAAO,IAAI,CAACF,MAAM,CAAC,cAAc,EAAEE,QAAQ,CAAC;EAC9C;EAEAmC,YAAYA,CAACnC,QAAoC,EAAE;IACjD,OAAO,IAAI,CAACF,MAAM,CAAC,MAAM,EAAEE,QAAQ,CAAC;EACtC;EAEAoC,kBAAkBA,CAACpC,QAA6B,EAAE;IAChD,OAAO,IAAI,CAACF,MAAM,CAAC,QAAQ,EAAEE,QAAQ,CAAC;EACxC;EAEAqC,iBAAiBA,CAACrC,QAA6B,EAAE;IAC/C,OAAO,IAAI,CAACF,MAAM,CAAC,QAAQ,EAAEE,QAAQ,CAAC;EACxC;EAEAsC,oBAAoBA,CAACtC,QAA6B,EAAE;IAClD,OAAO,IAAI,CAACF,MAAM,CAAC,WAAW,EAAEE,QAAQ,CAAC;EAC3C;EAEAuC,kBAAkBA,CAACvC,QAA6B,EAAE;IAChD,OAAO,IAAI,CAACF,MAAM,CAAC,SAAS,EAAEE,QAAQ,CAAC;EACzC;EAEAwC,yBAAyBA,CAACxC,QAAkC,EAAE;IAC5D,OAAO,IAAI,CAACF,MAAM,CAAC,aAAa,EAAEE,QAAQ,CAAC;EAC7C;EAEAyC,qBAAqBA,CAACzC,QAAiC,EAAe;IACpE,MAAM0C,IAAI,GAAG1D,GAAG,CAACO,QAAQ,EAAE,SAAS,IAAI,CAACG,MAAM,aAAa,CAAC;IAC7D,OAAOT,OAAO,CAACyD,IAAI,EAAGrC,QAAQ,IAAK;MACjCL,QAAQ,CAACK,QAAQ,CAACsC,IAAI,CAAC;IACzB,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAAC5C,QAA6B,EAAE;IAC3C,OAAO,IAAI,CAACF,MAAM,CAAC,OAAO,EAAEE,QAAQ,CAAC;EACvC;EAEA6C,kBAAkBA,CAAC7C,QAA6B,EAAE;IAChD,OAAO,IAAI,CAACF,MAAM,CAAC,WAAW,EAAEE,QAAQ,CAAC;EAC3C;EAEA8C,mBAAmBA,CAAC9C,QAA6B,EAAE;IACjD,OAAO,IAAI,CAACF,MAAM,CAAC,QAAQ,EAAEE,QAAQ,CAAC;EACxC;EAEA+C,kBAAkBA,CAAC/C,QAAoC,EAAE;IACvD,OAAO,IAAI,CAACF,MAAM,CAAC,YAAY,EAAEE,QAAQ,CAAC;EAC5C;EAEAgD,8BAA8BA,CAAChD,QAAyC,EAAE;IACxE,OAAO,IAAI,CAACF,MAAM,CAAC,wBAAwB,EAAEE,QAAQ,CAAC;EACxD;EAEA,MAAMiD,YAAYA,CAACC,KAAa,EAAiB;IAC/C,MAAMC,OAAO,GAAGnE,GAAG,CAACO,QAAQ,EAAE,SAAS,IAAI,CAACG,MAAM,aAAa,CAAC;IAChE,MAAM0D,IAAI,GAAG,MAAMhE,GAAG,CAAC+D,OAAO,CAAC;IAC/B,MAAME,OAAO,GAAGD,IAAI,CAAC5C,GAAG,CAAC,CAAC,IAAI,EAAE;IAChC,IAAI,CAAC6C,OAAO,CAACC,QAAQ,CAACJ,KAAK,CAAC,EAAE;MAC5B,MAAM/D,GAAG,CAACgE,OAAO,EAAE,CAAC,GAAGE,OAAO,EAAEH,KAAK,CAAC,CAAC;IACzC;EACF;EAEA,MAAMK,yBAAyBA,CAACC,KAAc,EAAE;IAC9C,MAAMhC,OAAO,GAAGxC,GAAG,CAACO,QAAQ,EAAE,SAAS,IAAI,CAACG,MAAM,yBAAyB,CAAC;IAC5E,MAAMP,GAAG,CAACqC,OAAO,EAAEgC,KAAK,CAAC;EAC3B;EAEA,MAAMC,eAAeA,CAACf,IAAY,EAAEnC,IAAS,EAAuB;IAClE,MAAMmD,OAAO,GAAG1E,GAAG,CAACO,QAAQ,EAAE,SAAS,IAAI,CAACG,MAAM,IAAIgD,IAAI,EAAE,CAAC;IAC7D,MAAMiB,iBAAiB,GAAGzE,YAAY,CAACwE,OAAO,CAAC;IAC/C,MAAMC,iBAAiB,CAACtE,MAAM,CAAC,CAAC;IAEhC,MAAMuE,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC1E,GAAG,CAACuE,OAAO,EAAE;QAAE,GAAGnD,IAAI;QAAEuD,UAAU,EAAExE,eAAe,CAAC;MAAE,CAAC,CAAC;IAC1D,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM;MACXyE,aAAa,CAACH,QAAQ,CAAC;MACvBD,iBAAiB,CAACK,MAAM,CAAC,CAAC;IAC5B,CAAC;EACH;EAEA,MAAMC,UAAUA,CAACvB,IAAY,EAAiB;IAC5C,MAAMlB,OAAO,GAAGxC,GAAG,CAACO,QAAQ,EAAE,SAAS,IAAI,CAACG,MAAM,IAAIgD,IAAI,EAAE,CAAC;IAC7D,MAAMrD,MAAM,CAACmC,OAAO,CAAC;EACvB;EAEA,MAAM0C,SAASA,CAACC,GAAW,EAAE5D,IAAS,EAAE;IACtC,MAAM6D,OAAO,GAAGpF,GAAG,CAACO,QAAQ,EAAE,SAAS,IAAI,CAACG,MAAM,YAAYyE,GAAG,EAAE,CAAC;IACpE,MAAMhF,GAAG,CAACiF,OAAO,EAAE;MACjBC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACrBhE;IACF,CAAC,CAAC;EACJ;AACF;AAACiE,qBAAA,GAhNYhF,oBAAoB;AAApBA,oBAAoB,CAChBI,SAAS,GAAsC,IAAI6E,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}