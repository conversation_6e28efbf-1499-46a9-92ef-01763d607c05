{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\pages\\\\Host\\\\Management\\\\HostRound1.tsx\";\nimport React from 'react';\nimport Host from '../../../layouts/Host/Host';\nimport QuestionBoxRound1 from '../../../layouts/RoundBase/Round1';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HostRound1 = () => {\n  return /*#__PURE__*/_jsxDEV(Host, {\n    QuestionComponent: /*#__PURE__*/_jsxDEV(QuestionBoxRound1, {\n      isHost: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 32\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 9\n  }, this);\n};\n_c = HostRound1;\nexport default HostRound1;\nvar _c;\n$RefreshReg$(_c, \"HostRound1\");", "map": {"version": 3, "names": ["React", "Host", "QuestionBoxRound1", "jsxDEV", "_jsxDEV", "HostRound1", "QuestionComponent", "isHost", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/pages/Host/Management/HostRound1.tsx"], "sourcesContent": ["import Round1 from '../../../layouts/RoundBase/Round1';\r\nimport React from 'react';\r\nimport Host from '../../../layouts/Host/Host';\r\nimport QuestionBoxRound1 from '../../../layouts/RoundBase/Round1';\r\n\r\nconst HostRound1: React.FC = () => {\r\n    return (\r\n        <Host\r\n            QuestionComponent={<QuestionBoxRound1 isHost={true}/>}\r\n        />\r\n    )\r\n};\r\n\r\nexport default HostRound1;\r\n"], "mappings": ";AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,4BAA4B;AAC7C,OAAOC,iBAAiB,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAC/B,oBACID,OAAA,CAACH,IAAI;IACDK,iBAAiB,eAAEF,OAAA,CAACF,iBAAiB;MAACK,MAAM,EAAE;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzD,CAAC;AAEV,CAAC;AAACC,EAAA,GANIP,UAAoB;AAQ1B,eAAeA,UAAU;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}