{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\layouts\\\\RoundBase\\\\Round1.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { useHost } from '../../context/hostContext';\nimport { useTimeStart } from '../../context/timeListenerContext';\nimport { usePlayer } from '../../context/playerContext';\nimport PlayerAnswerInput from '../../components/ui/PlayerAnswerInput';\nimport { useSounds } from '../../context/soundContext';\nimport { useFirebaseListener } from '../../shared/hooks';\nimport { useSelector } from 'react-redux';\n\n// interface QuestionBoxProps {\n//     question: string;\n//     imgUrl?: string;\n//     isHost?: boolean\n// }\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionBoxRound1 = ({\n  isHost,\n  isSpectator = false\n}) => {\n  _s();\n  const sounds = useSounds();\n  const [searchParams] = useSearchParams();\n  const roomId = searchParams.get(\"roomId\") || \"\";\n  const [currentQuestion, setCurrentQuestion] = useState();\n  const [correctAnswer, setCorrectAnswer] = useState(\"\");\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const {\n    timeLeft,\n    playerAnswerTime,\n    startTimer,\n    setTimeLeft\n  } = useTimeStart();\n  const {\n    setAnswerList,\n    playerAnswerRef,\n    position,\n    animationKey,\n    setAnimationKey,\n    currentPlayerName,\n    currentPlayerAvatar\n  } = usePlayer();\n  const {\n    currentAnswer\n  } = useHost();\n  const {\n    listenToTimeStart,\n    listenToSound,\n    listenToCurrentQuestion,\n    deletePath\n  } = useFirebaseListener(roomId);\n  const isInputDisabled = useSelector(state => state.game.isInputDisabled);\n  useEffect(() => {\n    console.log(\"playerAnswerRef.current\", playerAnswerRef.current);\n  }, [playerAnswerRef.current]);\n  const isInitialMount = useRef(false);\n  useEffect(() => {\n    const unsubscribe = listenToTimeStart(() => startTimer(10));\n    return () => {\n      unsubscribe();\n    };\n  }, []);\n  useEffect(() => {\n    const unsubscribeSound = listenToSound(() => {\n      deletePath(\"sound\");\n    });\n    return () => {\n      unsubscribeSound();\n    };\n  }, []);\n\n  // const isInitialTimerMount = useRef(true)\n  // useEffect(() => {\n  //     console.log(\"timeLeft\", timeLeft);\n  //     if (isInitialTimerMount.current) {\n  //         isInitialTimerMount.current = false;\n  //         return;\n  //     }\n  //     if (timeLeft === 0) {\n  //         setIsInputDisabled(true)\n  //         setAnimationKey((prev: number) => prev + 1);\n  //         if (!isHost && !isSpectator) {\n  //             console.log(\"playerAnswerRef.current\", playerAnswerRef.current);\n  //             console.log(\"position\", position);\n\n  //             // When timer runs out, do your clean up / game logic:\n  //             submitAnswer(roomId, playerAnswerRef.current, position, playerAnswerTime, currentPlayerName, currentPlayerAvatar)\n\n  //         }\n  //         // If you want to reset timer, call startTimer again here or leave stopped\n  //     }\n  // }, [timeLeft]);\n  // useEffect(() => {\n  //     const unsubscribeQuestion = listenToQuestions(roomId, (question) => {\n  //         setCurrentQuestion(question)\n  //         console.log(\"isHost\", isHost);\n\n  //         console.log(\"current correcr Answer\", currentAnswer);\n\n  //         if (isHost) {\n  //             setCorrectAnswer(currentAnswer)\n  //         }\n  //         console.log(\"current question\", question)\n  //         setAnswerList([])\n  //         if (!isHost) {\n  //             setCorrectAnswer(\"\")\n  //         }\n\n  //     });\n\n  //     // No need to set state here; it's handled by useState initializer\n  //     return () => {\n  //         unsubscribeQuestion();\n  //     };\n  // }, []);\n\n  // useEffect(() => {\n\n  //     const unsubscribePlayers = listenToAnswers(roomId, (answer) => {\n  //         const audio = sounds['correct'];\n  //         if (audio) {\n  //             audio.play();\n  //         }\n  //         setCorrectAnswer(`Đáp án: ${answer}`)\n  //     });\n\n  //     // No need to set state here; it's handled by useState initializer\n  //     return () => {\n  //         unsubscribePlayers();\n\n  //     };\n  // }, []);\n\n  // Watch for changes in currentAnswer from host context\n  // useEffect(() => {\n  //     if (isHost && currentAnswer) {\n  //         setCorrectAnswer(currentAnswer)\n  //         console.log(\"Host answer updated in Round 1:\", currentAnswer)\n  //     }\n  // }, [currentAnswer, isHost]);\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4 w-full flex flex-col items-center`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `text-white text-xl font-semibold text-center mb-4 max-w-[90%] ${isExpanded ? \"max-h-none\" : \"max-h-[120px] overflow-hidden\"}`,\n      children: currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.question\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `text-cyan-200 text-lg font-semibold text-center mb-4 max-w-[90%] ${isExpanded ? \"max-h-none\" : \"max-h-[120px] overflow-hidden\"}`,\n      children: correctAnswer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `w-full h-[300px] flex items-center justify-center overflow-hidden cursor-pointer mb-4  min-h-[400px]`,\n      onClick: () => setIsModalOpen(true),\n      children: (_url$split$pop => {\n        const url = currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.imgUrl;\n        if (!url) return /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white\",\n          children: \"No media\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 38\n        }, this);\n        const extension = ((_url$split$pop = url.split('.').pop()) === null || _url$split$pop === void 0 ? void 0 : _url$split$pop.toLowerCase()) || \"\";\n        if ([\"jpg\", \"jpeg\", \"png\", \"gif\", \"webp\"].includes(extension)) {\n          return /*#__PURE__*/_jsxDEV(\"img\", {\n            src: url,\n            alt: \"Question Visual\",\n            className: \"w-full h-full object-cover rounded-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 32\n          }, this);\n        }\n        if ([\"mp3\", \"wav\", \"ogg\"].includes(extension)) {\n          return /*#__PURE__*/_jsxDEV(\"audio\", {\n            controls: true,\n            className: \"w-full h-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"source\", {\n              src: url,\n              type: `audio/${extension}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 29\n            }, this), \"Your browser does not support the audio element.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 32\n          }, this);\n        }\n        if ([\"mp4\", \"webm\", \"ogg\"].includes(extension)) {\n          return /*#__PURE__*/_jsxDEV(\"video\", {\n            controls: true,\n            className: \"w-full h-full object-cover rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"source\", {\n              src: url,\n              type: `video/${extension}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 29\n            }, this), \"Your browser does not support the video tag.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 32\n          }, this);\n        }\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white\",\n          children: \"Unsupported media type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 28\n        }, this);\n      })()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 13\n    }, this), !isSpectator && /*#__PURE__*/_jsxDEV(PlayerAnswerInput, {\n      isHost: isHost,\n      question: currentQuestion,\n      isDisabled: isInputDisabled\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 17\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-80 flex justify-center items-center z-50\",\n      onClick: () => setIsModalOpen(false),\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.imgUrl,\n        alt: \"Full Size\",\n        className: \"max-w-full max-h-full rounded-xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 9\n  }, this);\n};\n\n// const Round1: React.FC<RoundBase> = ({ isHost }) => {\n//     return (\n//         <Play\n//             questionComponent={<QuestionBox question=\"Câu hỏi mẫu?\" imageUrl=\"https://a.travel-assets.com/findyours-php/viewfinder/images/res70/474000/474240-Left-Bank-Paris.jpg\" isHost={isHost} />}\n//             isHost={isHost}\n//         />\n//     );\n// }\n_s(QuestionBoxRound1, \"vGxxotkeyv1xMjInFdOSDVkvj/8=\", false, function () {\n  return [useSounds, useSearchParams, useTimeStart, usePlayer, useHost, useFirebaseListener, useSelector];\n});\n_c = QuestionBoxRound1;\nexport default QuestionBoxRound1;\nvar _c;\n$RefreshReg$(_c, \"QuestionBoxRound1\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useSearchParams", "useHost", "useTimeStart", "usePlayer", "PlayerAnswerInput", "useSounds", "useFirebaseListener", "useSelector", "jsxDEV", "_jsxDEV", "QuestionBoxRound1", "isHost", "isSpectator", "_s", "sounds", "searchParams", "roomId", "get", "currentQuestion", "setCurrentQuestion", "<PERSON><PERSON><PERSON><PERSON>", "setCorrectAnswer", "isExpanded", "setIsExpanded", "isModalOpen", "setIsModalOpen", "timeLeft", "playerAnswerTime", "startTimer", "setTimeLeft", "setAnswerList", "playerAnswerRef", "position", "animationKey", "setAnimationKey", "currentPlayerName", "currentPlayerAvatar", "currentAnswer", "listenToTimeStart", "listenToSound", "listenToCurrentQuestion", "deletePath", "isInputDisabled", "state", "game", "console", "log", "current", "isInitialMount", "unsubscribe", "unsubscribeSound", "className", "children", "question", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_url$split$pop", "url", "imgUrl", "extension", "split", "pop", "toLowerCase", "includes", "src", "alt", "controls", "type", "isDisabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/layouts/RoundBase/Round1.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react'\r\nimport Play from '../../layouts/Play'\r\nimport { RoundBase } from '../../type';\r\nimport { useSearchParams } from 'react-router-dom';\r\nimport { useHost } from '../../context/hostContext';\r\nimport { useTimeStart } from '../../context/timeListenerContext';\r\nimport { usePlayer } from '../../context/playerContext';\r\nimport PlayerAnswerInput from '../../components/ui/PlayerAnswerInput';\r\nimport { GameState, Question } from '../../shared/types';\r\nimport { submitAnswer } from '../services';\r\nimport { useSounds } from '../../context/soundContext';\r\nimport { set } from 'firebase/database';\r\nimport { useFirebaseListener } from '../../shared/hooks';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '../../app/store';\r\n\r\n\r\n\r\n// interface QuestionBoxProps {\r\n//     question: string;\r\n//     imgUrl?: string;\r\n//     isHost?: boolean\r\n// }\r\n\r\ninterface Round1Props {\r\n    isHost: boolean,\r\n    isSpectator?: boolean\r\n}\r\n\r\nconst QuestionBoxRound1: React.FC<Round1Props> = ({ isHost, isSpectator = false }) => {\r\n    const sounds = useSounds();\r\n    const [searchParams] = useSearchParams()\r\n    const roomId = searchParams.get(\"roomId\") || \"\"\r\n    const [currentQuestion, setCurrentQuestion] = useState<Question>()\r\n    const [correctAnswer, setCorrectAnswer] = useState<string>(\"\")\r\n    const [isExpanded, setIsExpanded] = useState(false);\r\n    const [isModalOpen, setIsModalOpen] = useState(false);\r\n    const { timeLeft, playerAnswerTime, startTimer, setTimeLeft } = useTimeStart();\r\n    const { setAnswerList, playerAnswerRef, position, animationKey, setAnimationKey, currentPlayerName, currentPlayerAvatar } = usePlayer()\r\n    const { currentAnswer } = useHost()\r\n    const { listenToTimeStart, listenToSound, listenToCurrentQuestion ,deletePath } = useFirebaseListener(roomId);\r\n    const isInputDisabled = useSelector((state: RootState) => state.game.isInputDisabled);\r\n    useEffect(() => {\r\n        console.log(\"playerAnswerRef.current\", playerAnswerRef.current);\r\n    }, [playerAnswerRef.current])\r\n\r\n    const isInitialMount = useRef(false)\r\n    useEffect(() => {\r\n        const unsubscribe = listenToTimeStart(\r\n            () => startTimer(10)\r\n        )\r\n        return () => {\r\n            unsubscribe();\r\n        };\r\n\r\n    }, [])\r\n\r\n    useEffect(() => {\r\n        const unsubscribeSound = listenToSound(\r\n            () => {\r\n                deletePath(\"sound\")\r\n            }\r\n        );\r\n\r\n        return () => {\r\n            unsubscribeSound();\r\n        };\r\n    }, []);\r\n\r\n    // const isInitialTimerMount = useRef(true)\r\n    // useEffect(() => {\r\n    //     console.log(\"timeLeft\", timeLeft);\r\n    //     if (isInitialTimerMount.current) {\r\n    //         isInitialTimerMount.current = false;\r\n    //         return;\r\n    //     }\r\n    //     if (timeLeft === 0) {\r\n    //         setIsInputDisabled(true)\r\n    //         setAnimationKey((prev: number) => prev + 1);\r\n    //         if (!isHost && !isSpectator) {\r\n    //             console.log(\"playerAnswerRef.current\", playerAnswerRef.current);\r\n    //             console.log(\"position\", position);\r\n\r\n\r\n    //             // When timer runs out, do your clean up / game logic:\r\n    //             submitAnswer(roomId, playerAnswerRef.current, position, playerAnswerTime, currentPlayerName, currentPlayerAvatar)\r\n\r\n    //         }\r\n    //         // If you want to reset timer, call startTimer again here or leave stopped\r\n    //     }\r\n    // }, [timeLeft]);\r\n    // useEffect(() => {\r\n    //     const unsubscribeQuestion = listenToQuestions(roomId, (question) => {\r\n    //         setCurrentQuestion(question)\r\n    //         console.log(\"isHost\", isHost);\r\n\r\n    //         console.log(\"current correcr Answer\", currentAnswer);\r\n\r\n    //         if (isHost) {\r\n    //             setCorrectAnswer(currentAnswer)\r\n    //         }\r\n    //         console.log(\"current question\", question)\r\n    //         setAnswerList([])\r\n    //         if (!isHost) {\r\n    //             setCorrectAnswer(\"\")\r\n    //         }\r\n\r\n    //     });\r\n\r\n    //     // No need to set state here; it's handled by useState initializer\r\n    //     return () => {\r\n    //         unsubscribeQuestion();\r\n    //     };\r\n    // }, []);\r\n\r\n    \r\n\r\n    // useEffect(() => {\r\n\r\n    //     const unsubscribePlayers = listenToAnswers(roomId, (answer) => {\r\n    //         const audio = sounds['correct'];\r\n    //         if (audio) {\r\n    //             audio.play();\r\n    //         }\r\n    //         setCorrectAnswer(`Đáp án: ${answer}`)\r\n    //     });\r\n\r\n    //     // No need to set state here; it's handled by useState initializer\r\n    //     return () => {\r\n    //         unsubscribePlayers();\r\n\r\n    //     };\r\n    // }, []);\r\n\r\n    // Watch for changes in currentAnswer from host context\r\n    // useEffect(() => {\r\n    //     if (isHost && currentAnswer) {\r\n    //         setCorrectAnswer(currentAnswer)\r\n    //         console.log(\"Host answer updated in Round 1:\", currentAnswer)\r\n    //     }\r\n    // }, [currentAnswer, isHost]);\r\n\r\n    return (\r\n        <div\r\n            className={`bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4 w-full flex flex-col items-center`}\r\n        >\r\n            {/* Question text */}\r\n            <div\r\n                className={`text-white text-xl font-semibold text-center mb-4 max-w-[90%] ${isExpanded ? \"max-h-none\" : \"max-h-[120px] overflow-hidden\"\r\n                    }`}\r\n            >\r\n                {currentQuestion?.question}\r\n            </div>\r\n\r\n            {/* Correct answer */}\r\n            <div\r\n                className={`text-cyan-200 text-lg font-semibold text-center mb-4 max-w-[90%] ${isExpanded ? \"max-h-none\" : \"max-h-[120px] overflow-hidden\"\r\n                    }`}\r\n            >\r\n                {correctAnswer}\r\n            </div>\r\n\r\n            {/* Media */}\r\n            <div\r\n                className={`w-full h-[300px] flex items-center justify-center overflow-hidden cursor-pointer mb-4  min-h-[400px]`}\r\n                onClick={() => setIsModalOpen(true)}\r\n            >\r\n                {(() => {\r\n                    const url = currentQuestion?.imgUrl;\r\n                    if (!url) return <p className=\"text-white\">No media</p>;\r\n\r\n                    const extension = url.split('.').pop()?.toLowerCase() || \"\";\r\n\r\n                    if ([\"jpg\", \"jpeg\", \"png\", \"gif\", \"webp\"].includes(extension)) {\r\n                        return <img src={url} alt=\"Question Visual\" className=\"w-full h-full object-cover rounded-lg\" />;\r\n                    }\r\n\r\n                    if ([\"mp3\", \"wav\", \"ogg\"].includes(extension)) {\r\n                        return <audio controls className=\"w-full h-full\">\r\n                            <source src={url} type={`audio/${extension}`} />\r\n                            Your browser does not support the audio element.\r\n                        </audio>;\r\n                    }\r\n\r\n                    if ([\"mp4\", \"webm\", \"ogg\"].includes(extension)) {\r\n                        return <video controls className=\"w-full h-full object-cover rounded-lg\">\r\n                            <source src={url} type={`video/${extension}`} />\r\n                            Your browser does not support the video tag.\r\n                        </video>;\r\n                    }\r\n\r\n                    return <p className=\"text-white\">Unsupported media type</p>;\r\n                })()}\r\n            </div>\r\n\r\n            {/* Answer input */}\r\n            {\r\n                !isSpectator &&\r\n                <PlayerAnswerInput\r\n                    isHost={isHost}\r\n                    question={currentQuestion}\r\n                    isDisabled={isInputDisabled}\r\n                />\r\n            }\r\n\r\n\r\n            {/* Modal for full-size image */}\r\n            {isModalOpen && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-80 flex justify-center items-center z-50\"\r\n                    onClick={() => setIsModalOpen(false)}>\r\n                    <img src={currentQuestion?.imgUrl} alt=\"Full Size\" className=\"max-w-full max-h-full rounded-xl\" />\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\n\r\n// const Round1: React.FC<RoundBase> = ({ isHost }) => {\r\n//     return (\r\n//         <Play\r\n//             questionComponent={<QuestionBox question=\"Câu hỏi mẫu?\" imageUrl=\"https://a.travel-assets.com/findyours-php/viewfinder/images/res70/474000/474240-Left-Bank-Paris.jpg\" isHost={isHost} />}\r\n//             isHost={isHost}\r\n//         />\r\n//     );\r\n// }\r\n\r\nexport default QuestionBoxRound1"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAG1D,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,SAAS,QAAQ,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,uCAAuC;AAGrE,SAASC,SAAS,QAAQ,4BAA4B;AAEtD,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,SAASC,WAAW,QAAQ,aAAa;;AAKzC;AACA;AACA;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,iBAAwC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,WAAW,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAClF,MAAMC,MAAM,GAAGT,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACU,YAAY,CAAC,GAAGf,eAAe,CAAC,CAAC;EACxC,MAAMgB,MAAM,GAAGD,YAAY,CAACE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC/C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAW,CAAC;EAClE,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAS,EAAE,CAAC;EAC9D,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAE6B,QAAQ;IAAEC,gBAAgB;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAG3B,YAAY,CAAC,CAAC;EAC9E,MAAM;IAAE4B,aAAa;IAAEC,eAAe;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,eAAe;IAAEC,iBAAiB;IAAEC;EAAoB,CAAC,GAAGjC,SAAS,CAAC,CAAC;EACvI,MAAM;IAAEkC;EAAc,CAAC,GAAGpC,OAAO,CAAC,CAAC;EACnC,MAAM;IAAEqC,iBAAiB;IAAEC,aAAa;IAAEC,uBAAuB;IAAEC;EAAW,CAAC,GAAGnC,mBAAmB,CAACU,MAAM,CAAC;EAC7G,MAAM0B,eAAe,GAAGnC,WAAW,CAAEoC,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAACF,eAAe,CAAC;EACrF5C,SAAS,CAAC,MAAM;IACZ+C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEf,eAAe,CAACgB,OAAO,CAAC;EACnE,CAAC,EAAE,CAAChB,eAAe,CAACgB,OAAO,CAAC,CAAC;EAE7B,MAAMC,cAAc,GAAGjD,MAAM,CAAC,KAAK,CAAC;EACpCD,SAAS,CAAC,MAAM;IACZ,MAAMmD,WAAW,GAAGX,iBAAiB,CACjC,MAAMV,UAAU,CAAC,EAAE,CACvB,CAAC;IACD,OAAO,MAAM;MACTqB,WAAW,CAAC,CAAC;IACjB,CAAC;EAEL,CAAC,EAAE,EAAE,CAAC;EAENnD,SAAS,CAAC,MAAM;IACZ,MAAMoD,gBAAgB,GAAGX,aAAa,CAClC,MAAM;MACFE,UAAU,CAAC,OAAO,CAAC;IACvB,CACJ,CAAC;IAED,OAAO,MAAM;MACTS,gBAAgB,CAAC,CAAC;IACtB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAGA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;;EAIA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,oBACIzC,OAAA;IACI0C,SAAS,EAAE,6HAA8H;IAAAC,QAAA,gBAGzI3C,OAAA;MACI0C,SAAS,EAAE,iEAAiE7B,UAAU,GAAG,YAAY,GAAG,+BAA+B,EAChI;MAAA8B,QAAA,EAENlC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNhD,OAAA;MACI0C,SAAS,EAAE,oEAAoE7B,UAAU,GAAG,YAAY,GAAG,+BAA+B,EACnI;MAAA8B,QAAA,EAENhC;IAAa;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAGNhD,OAAA;MACI0C,SAAS,EAAE,sGAAuG;MAClHO,OAAO,EAAEA,CAAA,KAAMjC,cAAc,CAAC,IAAI,CAAE;MAAA2B,QAAA,EAEnC,CAACO,cAAA,IAAM;QACJ,MAAMC,GAAG,GAAG1C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2C,MAAM;QACnC,IAAI,CAACD,GAAG,EAAE,oBAAOnD,OAAA;UAAG0C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;QAEvD,MAAMK,SAAS,GAAG,EAAAH,cAAA,GAAAC,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAAL,cAAA,uBAApBA,cAAA,CAAsBM,WAAW,CAAC,CAAC,KAAI,EAAE;QAE3D,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACJ,SAAS,CAAC,EAAE;UAC3D,oBAAOrD,OAAA;YAAK0D,GAAG,EAAEP,GAAI;YAACQ,GAAG,EAAC,iBAAiB;YAACjB,SAAS,EAAC;UAAuC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QACpG;QAEA,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACS,QAAQ,CAACJ,SAAS,CAAC,EAAE;UAC3C,oBAAOrD,OAAA;YAAO4D,QAAQ;YAAClB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5C3C,OAAA;cAAQ0D,GAAG,EAAEP,GAAI;cAACU,IAAI,EAAE,SAASR,SAAS;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oDAEpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QACZ;QAEA,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAACS,QAAQ,CAACJ,SAAS,CAAC,EAAE;UAC5C,oBAAOrD,OAAA;YAAO4D,QAAQ;YAAClB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpE3C,OAAA;cAAQ0D,GAAG,EAAEP,GAAI;cAACU,IAAI,EAAE,SAASR,SAAS;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gDAEpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QACZ;QAEA,oBAAOhD,OAAA;UAAG0C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAC/D,CAAC,EAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAIF,CAAC7C,WAAW,iBACZH,OAAA,CAACL,iBAAiB;MACdO,MAAM,EAAEA,MAAO;MACf0C,QAAQ,EAAEnC,eAAgB;MAC1BqD,UAAU,EAAE7B;IAAgB;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,EAKLjC,WAAW,iBACRf,OAAA;MAAK0C,SAAS,EAAC,4EAA4E;MACvFO,OAAO,EAAEA,CAAA,KAAMjC,cAAc,CAAC,KAAK,CAAE;MAAA2B,QAAA,eACrC3C,OAAA;QAAK0D,GAAG,EAAEjD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2C,MAAO;QAACO,GAAG,EAAC,WAAW;QAACjB,SAAS,EAAC;MAAkC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjG,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA5C,EAAA,CApMMH,iBAAwC;EAAA,QAC3BL,SAAS,EACDL,eAAe,EAM0BE,YAAY,EACgDC,SAAS,EAC3GF,OAAO,EACiDK,mBAAmB,EAC7EC,WAAW;AAAA;AAAAiE,EAAA,GAZjC9D,iBAAwC;AAsM9C,eAAeA,iBAAiB;AAAA,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}