{"ast": null, "code": "var _s = $RefreshSig$();\n// Firebase real-time listener hook\nimport { useEffect, useCallback } from 'react';\nimport { useAppDispatch } from '../../../app/store';\nimport { setPlayers, setCurrentQuestion, setScores, setRound2Grid, setRound4Grid, setIsInputDisabled } from '../../../app/store/slices/gameSlice';\nimport { setCurrentRoom, setPlayers as setRoomPlayers } from '../../../app/store/slices/roomSlice';\nimport { firebaseRealtimeService } from '../../services/firebase/realtime';\nimport { FirebaseRoomListener } from '../../../services/firebaseServices';\nexport const useFirebaseListener = roomId => {\n  _s();\n  const dispatch = useAppDispatch();\n  const listener = FirebaseRoomListener.getInstance(roomId || '');\n  /**\r\n   * Listen to room data changes\r\n   */\n  const listenToRoom = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRoom(roomId, data => {\n      if (data) {\n        // Update room state\n        dispatch(setCurrentRoom(data));\n\n        // Call optional callback\n        callback === null || callback === void 0 ? void 0 : callback(data);\n      }\n    });\n  }, [roomId, dispatch]);\n  const listenToTimeStart = useCallback(callback => {\n    if (!roomId) return () => {};\n    dispatch(setIsInputDisabled(false));\n    return listener.listenToTimeStart(callback);\n  }, [roomId, dispatch]);\n  const listenToSound = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToSound(callback);\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to player answers\r\n   */\n  const listenToPlayerAnswers = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToPlayerAnswers(roomId, answers => {\n      // Convert to array and update Redux state\n      const playersArray = Object.values(answers);\n      dispatch(setPlayers(playersArray));\n      dispatch(setRoomPlayers(playersArray.map(p => ({\n        ...p,\n        joinedAt: '',\n        isReady: true,\n        isConnected: true,\n        role: 'player'\n      }))));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(answers);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to current question\r\n   */\n  const listenToCurrentQuestion = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToQuestion(question => {\n      dispatch(setCurrentQuestion(question));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to correct answer\r\n   */\n  const listenToCorrectAnswer = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToCorrectAnswer(question => {\n      dispatch(setCurrentQuestion(question));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to scores\r\n   */\n  const listenToScores = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToScores(roomId, scores => {\n      dispatch(setScores(scores));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(scores);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to game state\r\n   */\n  const listenToGameState = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToGameState(roomId, state => {\n      if (state) {\n        // Update relevant Redux state based on game state\n        if (state.currentRound) {\n          // dispatch(setCurrentRound(state.currentRound));\n        }\n        if (state.isActive !== undefined) {\n          // dispatch(setIsActive(state.isActive));\n        }\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(state);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to Round 2 grid\r\n   */\n  const listenToRound2Grid = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRound2Grid(roomId, grid => {\n      if (grid) {\n        dispatch(setRound2Grid(grid));\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(grid);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to Round 4 grid\r\n   */\n  const listenToRound4Grid = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRound4Grid(roomId, grid => {\n      if (grid) {\n        dispatch(setRound4Grid(grid));\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(grid);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Setup all listeners at once\r\n   */\n  const setupAllListeners = useCallback(callbacks => {\n    if (!roomId) return () => {};\n    const unsubscribers = [listenToRoom(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRoomChange), listenToPlayerAnswers(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onPlayerAnswersChange), listenToCurrentQuestion(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onQuestionChange), listenToScores(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onScoresChange), listenToGameState(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onGameStateChange), listenToRound2Grid(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRound2GridChange), listenToRound4Grid(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRound4GridChange)];\n    return () => {\n      unsubscribers.forEach(unsubscribe => unsubscribe());\n    };\n  }, [roomId, listenToRoom, listenToPlayerAnswers, listenToCurrentQuestion, listenToScores, listenToGameState, listenToRound2Grid, listenToRound4Grid]);\n\n  /**\r\n   * Update player data\r\n   */\n  const updatePlayer = useCallback(async (playerId, playerData) => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updatePlayer(roomId, playerId, playerData);\n  }, [roomId]);\n\n  /**\r\n   * Set current question\r\n   */\n  const setCurrentQuestionFirebase = useCallback(async question => {\n    if (!roomId) return;\n    await firebaseRealtimeService.setCurrentQuestion(roomId, question);\n  }, [roomId]);\n\n  /**\r\n   * Update scores\r\n   */\n  const updateScoresFirebase = useCallback(async scores => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updateScores(roomId, scores);\n  }, [roomId]);\n\n  /**\r\n   * Update game state\r\n   */\n  const updateGameStateFirebase = useCallback(async gameState => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updateGameState(roomId, gameState);\n  }, [roomId]);\n  const deletePath = async path => {\n    await listener.deletePath(path);\n  };\n\n  /**\r\n   * Cleanup all listeners on unmount\r\n   */\n  useEffect(() => {\n    return () => {\n      firebaseRealtimeService.removeAllListeners();\n    };\n  }, []);\n  return {\n    // Listeners\n    listenToRoom,\n    listenToPlayerAnswers,\n    listenToCurrentQuestion,\n    listenToScores,\n    listenToGameState,\n    listenToRound2Grid,\n    listenToRound4Grid,\n    listenToTimeStart,\n    listenToSound,\n    setupAllListeners,\n    // Writers\n    updatePlayer,\n    setCurrentQuestionFirebase,\n    updateScoresFirebase,\n    updateGameStateFirebase,\n    //Delete\n    deletePath\n  };\n};\n_s(useFirebaseListener, \"EiYziqUchOHfPdmGGJ0aS48FB5k=\", false, function () {\n  return [useAppDispatch];\n});\nexport default useFirebaseListener;", "map": {"version": 3, "names": ["useEffect", "useCallback", "useAppDispatch", "setPlayers", "setCurrentQuestion", "setScores", "setRound2Grid", "setRound4Grid", "setIsInputDisabled", "setCurrentRoom", "setRoomPlayers", "firebaseRealtimeService", "FirebaseRoomListener", "useFirebaseListener", "roomId", "_s", "dispatch", "listener", "getInstance", "listenToRoom", "callback", "data", "listenToTimeStart", "listenToSound", "listenToPlayerAnswers", "answers", "players<PERSON><PERSON>y", "Object", "values", "map", "p", "joinedAt", "isReady", "isConnected", "role", "listenToCurrentQuestion", "listenToQuestion", "question", "listenToCorrectAnswer", "listenToScores", "scores", "listenToGameState", "state", "currentRound", "isActive", "undefined", "listenToRound2Grid", "grid", "listenToRound4Grid", "setupAllListeners", "callbacks", "unsubscribers", "onRoomChange", "onPlayerAnswersChange", "onQuestionChange", "onScoresChange", "onGameStateChange", "onRound2GridChange", "onRound4GridChange", "for<PERSON>ach", "unsubscribe", "updatePlayer", "playerId", "player<PERSON><PERSON>", "setCurrentQuestionFirebase", "updateScoresFirebase", "updateScores", "updateGameStateFirebase", "gameState", "updateGameState", "deletePath", "path", "removeAllListeners"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/hooks/firebase/useFirebaseListener.ts"], "sourcesContent": ["// Firebase real-time listener hook\r\nimport { useEffect, useCallback } from 'react';\r\nimport { useAppDispatch } from '../../../app/store';\r\nimport {\r\n  setPlayers,\r\n  setCurrentQuestion,\r\n  setScores,\r\n  setRound2Grid,\r\n  setRound4Grid,\r\n  setIsInputDisabled\r\n} from '../../../app/store/slices/gameSlice';\r\nimport {\r\n  setCurrentRoom,\r\n  setPlayers as setRoomPlayers\r\n} from '../../../app/store/slices/roomSlice';\r\nimport { firebaseRealtimeService } from '../../services/firebase/realtime';\r\nimport { PlayerData, Question, Score, Room } from '../../types';\r\nimport { FirebaseRoomListener } from '../../../services/firebaseServices';\r\n\r\nexport const useFirebaseListener = (roomId: string | null) => {\r\n  const dispatch = useAppDispatch();\r\n  const listener = FirebaseRoomListener.getInstance(roomId || '');\r\n  /**\r\n   * Listen to room data changes\r\n   */\r\n  const listenToRoom = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToRoom(roomId, (data) => {\r\n      if (data) {\r\n        // Update room state\r\n        dispatch(setCurrentRoom(data as Room));\r\n\r\n        // Call optional callback\r\n        callback?.(data);\r\n      }\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  const listenToTimeStart = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n    dispatch(setIsInputDisabled(false))\r\n\r\n    return listener.listenToTimeStart(\r\n      callback\r\n    );\r\n  }, [roomId, dispatch]);\r\n\r\n  const listenToSound = useCallback((callback: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToSound(\r\n      callback\r\n    );\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to player answers\r\n   */\r\n  const listenToPlayerAnswers = useCallback((callback?: (answers: Record<string, PlayerData>) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToPlayerAnswers(roomId, (answers) => {\r\n      // Convert to array and update Redux state\r\n      const playersArray = Object.values(answers);\r\n      dispatch(setPlayers(playersArray));\r\n      dispatch(setRoomPlayers(playersArray.map(p => ({ ...p, joinedAt: '', isReady: true, isConnected: true, role: 'player' as const }))));\r\n\r\n      // Call optional callback\r\n      callback?.(answers);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to current question\r\n   */\r\n  const listenToCurrentQuestion = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToQuestion((question) => {\r\n      dispatch(setCurrentQuestion(question));\r\n\r\n      // Call optional callback\r\n      callback?.();\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to correct answer\r\n   */\r\n  const listenToCorrectAnswer = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToCorrectAnswer((question) => {\r\n      dispatch(setCurrentQuestion(question));\r\n\r\n      // Call optional callback\r\n      callback?.();\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to scores\r\n   */\r\n  const listenToScores = useCallback((callback?: (scores: Score[]) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToScores(roomId, (scores) => {\r\n      dispatch(setScores(scores));\r\n\r\n      // Call optional callback\r\n      callback?.(scores);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to game state\r\n   */\r\n  const listenToGameState = useCallback((callback?: (state: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToGameState(roomId, (state) => {\r\n      if (state) {\r\n        // Update relevant Redux state based on game state\r\n        if (state.currentRound) {\r\n          // dispatch(setCurrentRound(state.currentRound));\r\n        }\r\n        if (state.isActive !== undefined) {\r\n          // dispatch(setIsActive(state.isActive));\r\n        }\r\n      }\r\n\r\n      // Call optional callback\r\n      callback?.(state);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to Round 2 grid\r\n   */\r\n  const listenToRound2Grid = useCallback((callback?: (grid: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToRound2Grid(roomId, (grid) => {\r\n      if (grid) {\r\n        dispatch(setRound2Grid(grid));\r\n      }\r\n\r\n      // Call optional callback\r\n      callback?.(grid);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to Round 4 grid\r\n   */\r\n  const listenToRound4Grid = useCallback((callback?: (grid: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToRound4Grid(roomId, (grid) => {\r\n      if (grid) {\r\n        dispatch(setRound4Grid(grid));\r\n      }\r\n\r\n      // Call optional callback\r\n      callback?.(grid);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Setup all listeners at once\r\n   */\r\n  const setupAllListeners = useCallback((callbacks?: {\r\n    onRoomChange?: (data: any) => void;\r\n    onPlayerAnswersChange?: (answers: Record<string, PlayerData>) => void;\r\n    onQuestionChange?: () => void;\r\n    onScoresChange?: (scores: Score[]) => void;\r\n    onGameStateChange?: (state: any) => void;\r\n    onRound2GridChange?: (grid: any) => void;\r\n    onRound4GridChange?: (grid: any) => void;\r\n  }) => {\r\n    if (!roomId) return () => { };\r\n\r\n    const unsubscribers = [\r\n      listenToRoom(callbacks?.onRoomChange),\r\n      listenToPlayerAnswers(callbacks?.onPlayerAnswersChange),\r\n      listenToCurrentQuestion(callbacks?.onQuestionChange),\r\n      listenToScores(callbacks?.onScoresChange),\r\n      listenToGameState(callbacks?.onGameStateChange),\r\n      listenToRound2Grid(callbacks?.onRound2GridChange),\r\n      listenToRound4Grid(callbacks?.onRound4GridChange),\r\n    ];\r\n\r\n    return () => {\r\n      unsubscribers.forEach(unsubscribe => unsubscribe());\r\n    };\r\n  }, [\r\n    roomId,\r\n    listenToRoom,\r\n    listenToPlayerAnswers,\r\n    listenToCurrentQuestion,\r\n    listenToScores,\r\n    listenToGameState,\r\n    listenToRound2Grid,\r\n    listenToRound4Grid,\r\n  ]);\r\n\r\n  /**\r\n   * Update player data\r\n   */\r\n  const updatePlayer = useCallback(async (playerId: string, playerData: Partial<PlayerData>) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.updatePlayer(roomId, playerId, playerData);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Set current question\r\n   */\r\n  const setCurrentQuestionFirebase = useCallback(async (question: Question) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.setCurrentQuestion(roomId, question);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Update scores\r\n   */\r\n  const updateScoresFirebase = useCallback(async (scores: Score[]) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.updateScores(roomId, scores);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Update game state\r\n   */\r\n  const updateGameStateFirebase = useCallback(async (gameState: any) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.updateGameState(roomId, gameState);\r\n  }, [roomId]);\r\n\r\n  const deletePath = async (path: string): Promise<void> => {\r\n    await listener.deletePath(path);\r\n  }\r\n\r\n  /**\r\n   * Cleanup all listeners on unmount\r\n   */\r\n  useEffect(() => {\r\n    return () => {\r\n      firebaseRealtimeService.removeAllListeners();\r\n    };\r\n  }, []);\r\n\r\n  return {\r\n    // Listeners\r\n    listenToRoom,\r\n    listenToPlayerAnswers,\r\n    listenToCurrentQuestion,\r\n    listenToScores,\r\n    listenToGameState,\r\n    listenToRound2Grid,\r\n    listenToRound4Grid,\r\n    listenToTimeStart,\r\n    listenToSound,\r\n    setupAllListeners,\r\n\r\n    // Writers\r\n    updatePlayer,\r\n    setCurrentQuestionFirebase,\r\n    updateScoresFirebase,\r\n    updateGameStateFirebase,\r\n\r\n    //Delete\r\n    deletePath\r\n  };\r\n};\r\n\r\nexport default useFirebaseListener;\r\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SACEC,UAAU,EACVC,kBAAkB,EAClBC,SAAS,EACTC,aAAa,EACbC,aAAa,EACbC,kBAAkB,QACb,qCAAqC;AAC5C,SACEC,cAAc,EACdN,UAAU,IAAIO,cAAc,QACvB,qCAAqC;AAC5C,SAASC,uBAAuB,QAAQ,kCAAkC;AAE1E,SAASC,oBAAoB,QAAQ,oCAAoC;AAEzE,OAAO,MAAMC,mBAAmB,GAAIC,MAAqB,IAAK;EAAAC,EAAA;EAC5D,MAAMC,QAAQ,GAAGd,cAAc,CAAC,CAAC;EACjC,MAAMe,QAAQ,GAAGL,oBAAoB,CAACM,WAAW,CAACJ,MAAM,IAAI,EAAE,CAAC;EAC/D;AACF;AACA;EACE,MAAMK,YAAY,GAAGlB,WAAW,CAAEmB,QAA8B,IAAK;IACnE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACQ,YAAY,CAACL,MAAM,EAAGO,IAAI,IAAK;MAC5D,IAAIA,IAAI,EAAE;QACR;QACAL,QAAQ,CAACP,cAAc,CAACY,IAAY,CAAC,CAAC;;QAEtC;QACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAEtB,MAAMM,iBAAiB,GAAGrB,WAAW,CAAEmB,QAAqB,IAAK;IAC/D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAC7BE,QAAQ,CAACR,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAEnC,OAAOS,QAAQ,CAACK,iBAAiB,CAC/BF,QACF,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAEtB,MAAMO,aAAa,GAAGtB,WAAW,CAAEmB,QAAoB,IAAK;IAC1D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACM,aAAa,CAC3BH,QACF,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMQ,qBAAqB,GAAGvB,WAAW,CAAEmB,QAAwD,IAAK;IACtG,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACa,qBAAqB,CAACV,MAAM,EAAGW,OAAO,IAAK;MACxE;MACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACH,OAAO,CAAC;MAC3CT,QAAQ,CAACb,UAAU,CAACuB,YAAY,CAAC,CAAC;MAClCV,QAAQ,CAACN,cAAc,CAACgB,YAAY,CAACG,GAAG,CAACC,CAAC,KAAK;QAAE,GAAGA,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE,IAAI;QAAEC,WAAW,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpI;MACAd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGK,OAAO,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACX,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMmB,uBAAuB,GAAGlC,WAAW,CAAEmB,QAAqB,IAAK;IACrE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACmB,gBAAgB,CAAEC,QAAQ,IAAK;MAC7CrB,QAAQ,CAACZ,kBAAkB,CAACiC,QAAQ,CAAC,CAAC;;MAEtC;MACAjB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMsB,qBAAqB,GAAGrC,WAAW,CAAEmB,QAAqB,IAAK;IACnE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACqB,qBAAqB,CAAED,QAAQ,IAAK;MAClDrB,QAAQ,CAACZ,kBAAkB,CAACiC,QAAQ,CAAC,CAAC;;MAEtC;MACAjB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMuB,cAAc,GAAGtC,WAAW,CAAEmB,QAAoC,IAAK;IAC3E,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAAC4B,cAAc,CAACzB,MAAM,EAAG0B,MAAM,IAAK;MAChExB,QAAQ,CAACX,SAAS,CAACmC,MAAM,CAAC,CAAC;;MAE3B;MACApB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGoB,MAAM,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1B,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMyB,iBAAiB,GAAGxC,WAAW,CAAEmB,QAA+B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAAC8B,iBAAiB,CAAC3B,MAAM,EAAG4B,KAAK,IAAK;MAClE,IAAIA,KAAK,EAAE;QACT;QACA,IAAIA,KAAK,CAACC,YAAY,EAAE;UACtB;QAAA;QAEF,IAAID,KAAK,CAACE,QAAQ,KAAKC,SAAS,EAAE;UAChC;QAAA;MAEJ;;MAEA;MACAzB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGsB,KAAK,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC5B,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM8B,kBAAkB,GAAG7C,WAAW,CAAEmB,QAA8B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACmC,kBAAkB,CAAChC,MAAM,EAAGiC,IAAI,IAAK;MAClE,IAAIA,IAAI,EAAE;QACR/B,QAAQ,CAACV,aAAa,CAACyC,IAAI,CAAC,CAAC;MAC/B;;MAEA;MACA3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG2B,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjC,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMgC,kBAAkB,GAAG/C,WAAW,CAAEmB,QAA8B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACqC,kBAAkB,CAAClC,MAAM,EAAGiC,IAAI,IAAK;MAClE,IAAIA,IAAI,EAAE;QACR/B,QAAQ,CAACT,aAAa,CAACwC,IAAI,CAAC,CAAC;MAC/B;;MAEA;MACA3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG2B,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjC,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMiC,iBAAiB,GAAGhD,WAAW,CAAEiD,SAQtC,IAAK;IACJ,IAAI,CAACpC,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,MAAMqC,aAAa,GAAG,CACpBhC,YAAY,CAAC+B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,YAAY,CAAC,EACrC5B,qBAAqB,CAAC0B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,qBAAqB,CAAC,EACvDlB,uBAAuB,CAACe,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEI,gBAAgB,CAAC,EACpDf,cAAc,CAACW,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,cAAc,CAAC,EACzCd,iBAAiB,CAACS,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEM,iBAAiB,CAAC,EAC/CV,kBAAkB,CAACI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEO,kBAAkB,CAAC,EACjDT,kBAAkB,CAACE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEQ,kBAAkB,CAAC,CAClD;IAED,OAAO,MAAM;MACXP,aAAa,CAACQ,OAAO,CAACC,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,CACD9C,MAAM,EACNK,YAAY,EACZK,qBAAqB,EACrBW,uBAAuB,EACvBI,cAAc,EACdE,iBAAiB,EACjBK,kBAAkB,EAClBE,kBAAkB,CACnB,CAAC;;EAEF;AACF;AACA;EACE,MAAMa,YAAY,GAAG5D,WAAW,CAAC,OAAO6D,QAAgB,EAAEC,UAA+B,KAAK;IAC5F,IAAI,CAACjD,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACkD,YAAY,CAAC/C,MAAM,EAAEgD,QAAQ,EAAEC,UAAU,CAAC;EAC1E,CAAC,EAAE,CAACjD,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMkD,0BAA0B,GAAG/D,WAAW,CAAC,MAAOoC,QAAkB,IAAK;IAC3E,IAAI,CAACvB,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACP,kBAAkB,CAACU,MAAM,EAAEuB,QAAQ,CAAC;EACpE,CAAC,EAAE,CAACvB,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMmD,oBAAoB,GAAGhE,WAAW,CAAC,MAAOuC,MAAe,IAAK;IAClE,IAAI,CAAC1B,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACuD,YAAY,CAACpD,MAAM,EAAE0B,MAAM,CAAC;EAC5D,CAAC,EAAE,CAAC1B,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMqD,uBAAuB,GAAGlE,WAAW,CAAC,MAAOmE,SAAc,IAAK;IACpE,IAAI,CAACtD,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAAC0D,eAAe,CAACvD,MAAM,EAAEsD,SAAS,CAAC;EAClE,CAAC,EAAE,CAACtD,MAAM,CAAC,CAAC;EAEZ,MAAMwD,UAAU,GAAG,MAAOC,IAAY,IAAoB;IACxD,MAAMtD,QAAQ,CAACqD,UAAU,CAACC,IAAI,CAAC;EACjC,CAAC;;EAED;AACF;AACA;EACEvE,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXW,uBAAuB,CAAC6D,kBAAkB,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL;IACArD,YAAY;IACZK,qBAAqB;IACrBW,uBAAuB;IACvBI,cAAc;IACdE,iBAAiB;IACjBK,kBAAkB;IAClBE,kBAAkB;IAClB1B,iBAAiB;IACjBC,aAAa;IACb0B,iBAAiB;IAEjB;IACAY,YAAY;IACZG,0BAA0B;IAC1BC,oBAAoB;IACpBE,uBAAuB;IAEvB;IACAG;EACF,CAAC;AACH,CAAC;AAACvD,EAAA,CAnQWF,mBAAmB;EAAA,QACbX,cAAc;AAAA;AAoQjC,eAAeW,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}