{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\layouts\\\\Play.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport Header from './Header';\nimport { usePlayer } from '../context/playerContext';\nimport { deletePath, listenToSpectatorJoin, listenToAnswers, setupOnDisconnect, listenToRoundRules } from '../services/firebaseServices';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport { useHost } from '../context/hostContext';\nimport RulesModal from '../components/RulesModal';\nimport { useFirebaseListener } from '../shared/hooks';\nimport { useTimeStart } from '../context/timeListenerContext';\nimport '../index.css';\nimport { useAppDispatch } from '../app/store';\nimport { setCurrentCorrectAnswer, clearPlayerAnswerList } from '../app/store/slices/gameSlice';\nimport { useSounds } from '../context/soundContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Play = ({\n  questionComponent,\n  isHost = false,\n  PlayerScore,\n  SideBar\n}) => {\n  _s();\n  var _roundTabs$find;\n  const roundTabs = [{\n    key: \"1\",\n    label: \"NHỔ NEO\"\n  }, {\n    key: \"2\",\n    label: \"VƯỢT SÓNG\"\n  }, {\n    key: \"3\",\n    label: \"BỨT PHÁ\"\n  }, {\n    key: \"4\",\n    label: \"CHINH PHỤC\"\n  }, {\n    key: \"summary\",\n    label: \"Tổng kết điểm\"\n  }, {\n    key: \"turn\",\n    label: \"Phân lượt\"\n  }];\n  const roundTime = {\n    \"1\": 10,\n    \"2\": 15,\n    \"3\": 60,\n    \"4\": 15,\n    \"turn\": 10\n  };\n  const navigate = useNavigate();\n  const playerAnswerRef = useRef(\"\");\n  const sounds = useSounds();\n  const [isChatOpen, setIsChatOpen] = useState(false);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [spectatorCount, setSpectatorCount] = useState(0);\n  const [showRulesModal, setShowRulesModal] = useState(false);\n  const [rulesRound, setRulesRound] = useState(\"1\");\n  const [userId, setUserId] = useState(localStorage.getItem(\"userId\"));\n  const [params] = useSearchParams();\n  const round = params.get(\"round\") || \"1\";\n  const {\n    players,\n    setPlayers,\n    setRoomId,\n    playersArray,\n    roomRules,\n    setRoomRules,\n    setPlayerArray,\n    position,\n    setCurrentQuestion,\n    selectedTopic,\n    setSelectedTopic,\n    setScoreList,\n    setAnswerList\n  } = usePlayer();\n  const {\n    playerScores,\n    setPlayerScores,\n    animationKey,\n    setAnimationKey,\n    mode,\n    rules\n  } = useHost();\n  const isMounted = useRef(false);\n  const {\n    timeLeft,\n    startTimer\n  } = useTimeStart();\n  const [searchParams] = useSearchParams();\n  const currentRound = searchParams.get(\"round\") || \"1\";\n  const testName = searchParams.get(\"testName\") || \"1\";\n  const roomId = searchParams.get(\"roomId\") || \"\";\n  const {\n    listenToCurrentQuestion,\n    listenToCorrectAnswer\n  } = useFirebaseListener(roomId);\n  const dispatch = useAppDispatch();\n  const isInitialMount = useRef(true);\n  const styles = `\n  @keyframes shrink {\n    from {\n      width: 100%;\n    }\n    to {\n      width: 0%;\n    }\n  }\n`;\n  // useEffect(() => {\n  //     if (isInitialMount.current) {\n  //         isInitialMount.current = false; // Allow subsequent runs\n  //         return;\n  //     }\n  //     setAnimationKey((prev) => prev + 1);\n  //     if (round === \"1\") {\n  //         console.log(\"start timer for round 1\");\n  //         startTimer(10);\n\n  //     }\n  //     if (round === \"2\") {\n  //         console.log(\"start timer for round 2\");\n  //         startTimer(15);\n  //     }\n  //     if (round === \"3\") {\n  //         console.log(\"start timer for round 3\");\n  //         startTimer(60);\n  //     }\n  //     if (round === \"4\") {\n  //         console.log(\"start timer for round 4\");\n  //         startTimer(15);\n  //     }\n  // }, [round]);\n\n  useEffect(() => {\n    const unsubscribePlayers = listenToCorrectAnswer(() => {\n      setAnimationKey(prev => prev + 1);\n    });\n  }, []);\n  useEffect(() => {\n    const unsubscribeQuestion = listenToCurrentQuestion(() => {\n      if (!isHost) {\n        dispatch(setCurrentCorrectAnswer(\"\"));\n      }\n      dispatch(clearPlayerAnswerList());\n    });\n    return () => {\n      unsubscribeQuestion();\n    };\n  }, [currentRound]);\n  useEffect(() => {\n    const unsubscribeAnswer = listenToAnswers(() => {\n      const audio = sounds['correct'];\n      if (audio) {\n        audio.play();\n      }\n    });\n    return () => {\n      unsubscribeAnswer();\n    };\n  }, []);\n  const handleRoundChange = async delta => {\n    console.log(\"currentRound\", currentRound);\n    const newRound = parseInt(currentRound) + delta;\n    console.log(\"new round\", newRound);\n    if (newRound >= 1 && newRound <= 4) {\n      // limit to 1-4 rounds\n      navigate(`?round=${newRound}&testName=${testName}&roomId=${roomId}`);\n    }\n\n    // Clear frontend state\n    setAnswerList([]);\n\n    // Clear Firebase data\n    await deletePath(roomId, \"questions\");\n    await deletePath(roomId, \"answers\");\n    await deletePath(roomId, \"answerLists\"); // Clear answer lists\n    await deletePath(roomId, \"turn\"); // Clear turn assignments\n    await deletePath(roomId, \"isModified\"); // Clear isModified state\n  };\n  useEffect(() => {\n    if (!roomId || !userId) return;\n\n    // Setup onDisconnect to remove user from room when connection lost\n    const currentPlayer = JSON.parse(localStorage.getItem(\"currentPlayer\") || \"{}\");\n    const cancelOnDisconnect = setupOnDisconnect(roomId, userId, currentPlayer);\n    return () => {\n      // Optional: cancel onDisconnect if component unmounts normally\n      cancelOnDisconnect();\n    };\n  }, [roomId, userId]);\n  useEffect(() => {\n    const unsubscribeSpectator = listenToSpectatorJoin(roomId, count => {\n      console.log(\"spectator\", count);\n      setSpectatorCount(count);\n    });\n    return () => {\n      unsubscribeSpectator();\n    };\n  }, []);\n  useEffect(() => {\n    const unsubscribeRules = listenToRoundRules(roomId, data => {\n      console.log(\"Rules data received:\", data);\n      setRoomRules(data);\n\n      // Show modal when host triggers it, regardless of round matching\n      if (data && data.show) {\n        setRulesRound(data.round);\n        setShowRulesModal(true);\n      } else {\n        setShowRulesModal(false);\n      }\n    });\n    return () => {\n      unsubscribeRules();\n    };\n  }, [roomId]);\n\n  // Remove auto-clear logic to allow proper modal synchronization between clients\n\n  const isFull = useRef(false);\n  // useEffect(() => {\n  //     const unsubscribePlayers = listenToPlayers(roomId, (updatedPlayers) => {\n  //         console.log(\"updatedPlayers\", updatedPlayers)\n  //         console.log(\"Object.keys(updatedPlayers)\", Object.keys(updatedPlayers))\n  //         console.log(\"Object.keys(updatedPlayers).length\", Object.keys(updatedPlayers).length)\n  //         if (updatedPlayers && Object.keys(updatedPlayers).length > 0) {\n  //             const playersList = Object.values(updatedPlayers);\n  //             console.log(\"playersList\", playersList);\n\n  //             const initialScoreList = [...playersList]\n  //             const scoreInitKey = `scoreInit_${roomId}_round1`;\n  //             const isFull = localStorage.getItem(`is_${roomId}_full`)\n\n  //             if (isFull != \"true\") {\n  //                 console.log(\"isFull inside\", isFull);\n\n  //                 console.log(\"initialScoreList\", initialScoreList);\n\n  //                 for (var score of initialScoreList) {\n  //                     score[\"score\"] = \"0\";\n  //                     score[\"isCorrect\"] = false;\n  //                     score[\"isModified\"] = false\n  //                 }\n  //                 console.log(\"initialScoreList\", initialScoreList);\n  //                 setScoreList(initialScoreList)\n  //                 setPlayerScores(initialScoreList)\n\n  //                 if (initialScoreList.length == 2) {\n  //                     localStorage.setItem(`is_${roomId}_full`, \"true\")\n  //                 }\n  //             }\n\n  //             // const currentPlayer = playersList.find((player: any) => player.uid === userId);\n  //             // const now = new Date().getTime();\n  //             // if(currentPlayer.lastActive && (now - currentPlayer.lastActive) > 10) {\n  //             //     return;\n  //             // }\n\n  //             setPlayerArray(playersList);\n  //             localStorage.setItem(\"playerList\", JSON.stringify(playersList));\n  //             console.log(\"Updated localStorage:\", localStorage.getItem(\"playerList\"));\n  //         } else {\n  //             console.log(\"Room is empty or players node deleted\");\n  //             console.log(\"roomId\", roomId);\n\n  //             setPlayerArray([]); // Clear state\n  //             localStorage.removeItem(\"playerList\"); // Clear localStorage\n  //         }\n  //     });\n\n  //     // No need to set state here; it's handled by useState initializer\n  //     return () => {\n  //         unsubscribePlayers();\n  //     };\n  // }, []);\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative \",\n    style: {\n      zoom: \"0.75\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-gradient-to-b from-slate-900 via-blue-900 to-blue-600\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,255,255,0.3)_1px,transparent_1px),radial-gradient(circle_at_75%_75%,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[length:100px_100px]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-blue-500/50 to-transparent\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 flex flex-col min-h-full\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        isHost: isHost,\n        spectatorCount: spectatorCount\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-1 p-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full lg:w-4/5 flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full h-3 bg-slate-700/50 rounded-full mb-4 border border-blue-400/30 shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-full bg-gradient-to-r from-blue-400 to-cyan-300 rounded-full shadow-inner\",\n              style: {\n                width: timeLeft > 0 ? '100%' : '100%',\n                // Always reset to 100% width\n                animation: timeLeft > 0 ? `shrink ${roundTime[round]}s linear forwards` : 'none',\n                animationPlayState: timeLeft > 0 ? 'running' : 'paused'\n              }\n            }, animationKey, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4  ${isHost ? \"min-h-[400px]\" : \"min-h-[400px]\"}`,\n            children: questionComponent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-slate-800/70 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-xl\",\n            children: PlayerScore\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:flex lg:w-1/5 flex-col gap-4\",\n          children: [!isHost && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 to-cyan-500 text-white text-center font-bold text-lg p-4 rounded-xl shadow-xl border border-blue-400/50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm opacity-90 mb-1\",\n              children: \"V\\xF2ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl\",\n              children: ((_roundTabs$find = roundTabs.find(tab => tab.key === currentRound)) === null || _roundTabs$find === void 0 ? void 0 : _roundTabs$find.label) || \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-slate-800/70 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-xl p-4 flex-1\",\n            children: SideBar\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"fixed bottom-6 right-6 bg-gradient-to-r from-blue-600 to-cyan-500 text-white w-14 h-14 flex items-center justify-center rounded-full shadow-2xl border-2 border-blue-400/50 hover:scale-110 transition-transform duration-200\",\n        onClick: () => setIsChatOpen(!isChatOpen),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xl\",\n          children: isChatOpen ? \"✖\" : \"💬\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 17\n      }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-slate-900/90 backdrop-blur-sm flex justify-center items-center z-50\",\n        onClick: () => setIsModalOpen(false),\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://a.travel-assets.com/findyours-php/viewfinder/images/res70/474000/474240-Left-Bank-Paris.jpg\",\n          alt: \"Full Size\",\n          className: \"max-w-full max-h-full rounded-xl shadow-2xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(RulesModal, {\n      isOpen: showRulesModal,\n      onClose: () => {\n        setShowRulesModal(false);\n        // Don't clear Firebase data - let each client manage their own modal state\n      },\n      round: rulesRound,\n      mode: mode,\n      roomRules: rules\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 282,\n    columnNumber: 9\n  }, this);\n};\n_s(Play, \"FlsHIeZHYNP2NIXtSjq7+T6bHsg=\", false, function () {\n  return [useNavigate, useSounds, useSearchParams, usePlayer, useHost, useTimeStart, useSearchParams, useFirebaseListener, useAppDispatch];\n});\n_c = Play;\nexport default Play;\nvar _c;\n$RefreshReg$(_c, \"Play\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Header", "usePlayer", "deletePath", "listenToSpectatorJoin", "listenToAnswers", "setupOnDisconnect", "listenToRoundRules", "useNavigate", "useSearchParams", "useHost", "RulesModal", "useFirebaseListener", "useTimeStart", "useAppDispatch", "setCurrentCorrectAnswer", "clearPlayerAnswerList", "useSounds", "jsxDEV", "_jsxDEV", "Play", "questionComponent", "isHost", "PlayerScore", "SideBar", "_s", "_roundTabs$find", "roundTabs", "key", "label", "roundTime", "navigate", "playerAnswerRef", "sounds", "isChatOpen", "setIsChatOpen", "isModalOpen", "setIsModalOpen", "spectatorCount", "setSpectatorCount", "showRulesModal", "setShowRulesModal", "rulesRound", "setRulesRound", "userId", "setUserId", "localStorage", "getItem", "params", "round", "get", "players", "setPlayers", "setRoomId", "players<PERSON><PERSON>y", "roomRules", "setRoomRules", "setPlayerArray", "position", "setCurrentQuestion", "selectedTopic", "setSelectedTopic", "setScoreList", "setAnswerList", "playerScores", "setPlayerScores", "animationKey", "setAnimationKey", "mode", "rules", "isMounted", "timeLeft", "startTimer", "searchParams", "currentRound", "testName", "roomId", "listenToCurrentQuestion", "listenToCorrectAnswer", "dispatch", "isInitialMount", "styles", "unsubscribePlayers", "prev", "unsubscribeQuestion", "unsubscribeAnswer", "audio", "play", "handleRoundChange", "delta", "console", "log", "newRound", "parseInt", "currentPlayer", "JSON", "parse", "cancelOnDisconnect", "unsubscribeSpectator", "count", "unsubscribeRules", "data", "show", "isFull", "className", "style", "zoom", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "animation", "animationPlayState", "find", "tab", "onClick", "src", "alt", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/layouts/Play.tsx"], "sourcesContent": ["import React, { useState, useEffect, ReactNode, useRef, useCallback } from 'react';\r\nimport Header from './Header';\r\nimport { usePlayer } from '../context/playerContext';\r\nimport { Answer, User } from '../type';\r\nimport { deletePath, addPlayerToRoom, listenToRules,listenToPlayers, listenToSpectatorJoin, listenToScores, listenToAnswers, listenToTimeStart, listenToBroadcastedAnswer, setupOnDisconnect, listenToRoundStart, listenToRoundRules } from '../services/firebaseServices';\r\nimport { useNavigate, useSearchParams } from 'react-router-dom';\r\nimport { submitAnswer } from './services';\r\nimport { getNextQuestion } from '../pages/Host/Test/service';\r\nimport { useHost } from '../context/hostContext';\r\nimport HostManagement from '../components/HostManagement';\r\nimport PlayerScore from '../components/PlayerScore';\r\nimport RulesModal from '../components/RulesModal';\r\nimport HostScore from '../components/PlayerAnswer';\r\nimport { setCurrentPacketQuestion } from '../components/services';\r\nimport { useFirebaseListener } from '../shared/hooks';\r\nimport { useTimeStart } from '../context/timeListenerContext';\r\nimport {\r\n    EyeIcon,\r\n} from \"@heroicons/react/24/solid\";\r\nimport '../index.css';\r\nimport { useAppDispatch, useAppSelector } from '../app/store';\r\nimport { setCurrentCorrectAnswer, clearPlayerAnswerList } from '../app/store/slices/gameSlice';\r\nimport { useSounds } from '../context/soundContext';\r\n\r\ninterface PlayProps {\r\n    questionComponent: ReactNode;\r\n    isHost?: boolean;\r\n    PlayerScore: ReactNode\r\n    SideBar: ReactNode\r\n}\r\n\r\ninterface Player {\r\n    score: number;\r\n    index: number;\r\n    username: string;\r\n    position: number;\r\n}\r\n\r\nconst Play: React.FC<PlayProps> = ({ questionComponent, isHost = false, PlayerScore, SideBar }) => {\r\n\r\n\r\n    const roundTabs = [\r\n        { key: \"1\", label: \"NHỔ NEO\" },\r\n        { key: \"2\", label: \"VƯỢT SÓNG\" },\r\n        { key: \"3\", label: \"BỨT PHÁ\" },\r\n        { key: \"4\", label: \"CHINH PHỤC\" },\r\n        { key: \"summary\", label: \"Tổng kết điểm\" },\r\n        { key: \"turn\", label: \"Phân lượt\" },\r\n    ];\r\n\r\n    const roundTime = {\r\n        \"1\": 10,\r\n        \"2\": 15,\r\n        \"3\": 60,\r\n        \"4\": 15,\r\n        \"turn\": 10,\r\n    }\r\n\r\n    const navigate = useNavigate()\r\n    const playerAnswerRef = useRef(\"\");\r\n    const sounds = useSounds();\r\n    const [isChatOpen, setIsChatOpen] = useState(false);\r\n    const [isModalOpen, setIsModalOpen] = useState(false);\r\n    const [spectatorCount, setSpectatorCount] = useState<number>(0)\r\n    const [showRulesModal, setShowRulesModal] = useState(false);\r\n    const [rulesRound, setRulesRound] = useState(\"1\");\r\n    const [userId, setUserId] = useState(localStorage.getItem(\"userId\"))\r\n    const [params] = useSearchParams()\r\n    const round = (params.get(\"round\") as \"1\" | \"2\" | \"3\" | \"4\" | \"turn\") || \"1\"\r\n    const { players, setPlayers, setRoomId, playersArray, roomRules, setRoomRules, setPlayerArray, position, setCurrentQuestion, selectedTopic, setSelectedTopic, setScoreList, setAnswerList } = usePlayer()\r\n    const { playerScores, setPlayerScores, animationKey, setAnimationKey, mode, rules } = useHost()\r\n    const isMounted = useRef(false);\r\n    const { timeLeft, startTimer } = useTimeStart();\r\n\r\n\r\n    const [searchParams] = useSearchParams();\r\n\r\n\r\n    const currentRound = searchParams.get(\"round\") || \"1\";\r\n    const testName = searchParams.get(\"testName\") || \"1\"\r\n    const roomId = searchParams.get(\"roomId\") || \"\";\r\n\r\n    const {listenToCurrentQuestion, listenToCorrectAnswer} = useFirebaseListener(roomId);\r\n    const dispatch = useAppDispatch();\r\n    const isInitialMount = useRef(true);\r\n    const styles = `\r\n  @keyframes shrink {\r\n    from {\r\n      width: 100%;\r\n    }\r\n    to {\r\n      width: 0%;\r\n    }\r\n  }\r\n`;\r\n    // useEffect(() => {\r\n    //     if (isInitialMount.current) {\r\n    //         isInitialMount.current = false; // Allow subsequent runs\r\n    //         return;\r\n    //     }\r\n    //     setAnimationKey((prev) => prev + 1);\r\n    //     if (round === \"1\") {\r\n    //         console.log(\"start timer for round 1\");\r\n    //         startTimer(10);\r\n\r\n    //     }\r\n    //     if (round === \"2\") {\r\n    //         console.log(\"start timer for round 2\");\r\n    //         startTimer(15);\r\n    //     }\r\n    //     if (round === \"3\") {\r\n    //         console.log(\"start timer for round 3\");\r\n    //         startTimer(60);\r\n    //     }\r\n    //     if (round === \"4\") {\r\n    //         console.log(\"start timer for round 4\");\r\n    //         startTimer(15);\r\n    //     }\r\n    // }, [round]);\r\n\r\n    useEffect(()=> {\r\n        const unsubscribePlayers = listenToCorrectAnswer(() => {\r\n            setAnimationKey((prev) => prev + 1);\r\n        })\r\n    },[])\r\n\r\n    useEffect(() => {\r\n        const unsubscribeQuestion = listenToCurrentQuestion(() => {\r\n            if(!isHost) {\r\n                dispatch(setCurrentCorrectAnswer(\"\"))\r\n            }\r\n\r\n            dispatch(clearPlayerAnswerList())\r\n        })\r\n\r\n        return () => {\r\n            unsubscribeQuestion()\r\n        }\r\n    },[currentRound])\r\n\r\n    useEffect(() => {\r\n        const unsubscribeAnswer = listenToAnswers(\r\n            () => {\r\n                const audio = sounds['correct'];\r\n                if (audio) {\r\n                    audio.play();\r\n                }\r\n            }\r\n        );\r\n\r\n        return () => {\r\n            unsubscribeAnswer();\r\n        };\r\n    }, []);\r\n\r\n\r\n    const handleRoundChange = async (delta: number) => {\r\n        console.log(\"currentRound\", currentRound)\r\n        const newRound = parseInt(currentRound) + delta;\r\n        console.log(\"new round\", newRound)\r\n        if (newRound >= 1 && newRound <= 4) { // limit to 1-4 rounds\r\n            navigate(`?round=${newRound}&testName=${testName}&roomId=${roomId}`);\r\n        }\r\n\r\n        // Clear frontend state\r\n        setAnswerList([]);\r\n\r\n        // Clear Firebase data\r\n        await deletePath(roomId, \"questions\");\r\n        await deletePath(roomId, \"answers\");\r\n        await deletePath(roomId, \"answerLists\"); // Clear answer lists\r\n        await deletePath(roomId, \"turn\"); // Clear turn assignments\r\n        await deletePath(roomId, \"isModified\"); // Clear isModified state\r\n    };\r\n\r\n\r\n    useEffect(() => {\r\n        if (!roomId || !userId) return;\r\n\r\n        // Setup onDisconnect to remove user from room when connection lost\r\n        const currentPlayer = JSON.parse(localStorage.getItem(\"currentPlayer\") || \"{}\");\r\n        const cancelOnDisconnect = setupOnDisconnect(roomId, userId, currentPlayer);\r\n\r\n        return () => {\r\n            // Optional: cancel onDisconnect if component unmounts normally\r\n            cancelOnDisconnect();\r\n        };\r\n    }, [roomId, userId]);\r\n\r\n    useEffect(() => {\r\n        const unsubscribeSpectator = listenToSpectatorJoin(roomId, (count) => {\r\n            console.log(\"spectator\", count);\r\n            \r\n            setSpectatorCount(count)\r\n        })\r\n\r\n        return () => {\r\n            unsubscribeSpectator()\r\n        }\r\n    }, [])\r\n\r\n    useEffect(() => {\r\n        const unsubscribeRules = listenToRoundRules(roomId, (data) => {\r\n            console.log(\"Rules data received:\", data);\r\n            setRoomRules(data)\r\n\r\n            // Show modal when host triggers it, regardless of round matching\r\n            if (data && data.show) {\r\n                setRulesRound(data.round);\r\n                setShowRulesModal(true);\r\n            } else {\r\n                setShowRulesModal(false);\r\n            }\r\n        })\r\n\r\n        return () => {\r\n            unsubscribeRules()\r\n        }\r\n    }, [roomId])\r\n\r\n    // Remove auto-clear logic to allow proper modal synchronization between clients\r\n\r\n    const isFull = useRef(false)\r\n    // useEffect(() => {\r\n    //     const unsubscribePlayers = listenToPlayers(roomId, (updatedPlayers) => {\r\n    //         console.log(\"updatedPlayers\", updatedPlayers)\r\n    //         console.log(\"Object.keys(updatedPlayers)\", Object.keys(updatedPlayers))\r\n    //         console.log(\"Object.keys(updatedPlayers).length\", Object.keys(updatedPlayers).length)\r\n    //         if (updatedPlayers && Object.keys(updatedPlayers).length > 0) {\r\n    //             const playersList = Object.values(updatedPlayers);\r\n    //             console.log(\"playersList\", playersList);\r\n\r\n    //             const initialScoreList = [...playersList]\r\n    //             const scoreInitKey = `scoreInit_${roomId}_round1`;\r\n    //             const isFull = localStorage.getItem(`is_${roomId}_full`)\r\n\r\n    //             if (isFull != \"true\") {\r\n    //                 console.log(\"isFull inside\", isFull);\r\n\r\n    //                 console.log(\"initialScoreList\", initialScoreList);\r\n\r\n    //                 for (var score of initialScoreList) {\r\n    //                     score[\"score\"] = \"0\";\r\n    //                     score[\"isCorrect\"] = false;\r\n    //                     score[\"isModified\"] = false\r\n    //                 }\r\n    //                 console.log(\"initialScoreList\", initialScoreList);\r\n    //                 setScoreList(initialScoreList)\r\n    //                 setPlayerScores(initialScoreList)\r\n\r\n    //                 if (initialScoreList.length == 2) {\r\n    //                     localStorage.setItem(`is_${roomId}_full`, \"true\")\r\n    //                 }\r\n    //             }\r\n\r\n    //             // const currentPlayer = playersList.find((player: any) => player.uid === userId);\r\n    //             // const now = new Date().getTime();\r\n    //             // if(currentPlayer.lastActive && (now - currentPlayer.lastActive) > 10) {\r\n    //             //     return;\r\n    //             // }\r\n\r\n    //             setPlayerArray(playersList);\r\n    //             localStorage.setItem(\"playerList\", JSON.stringify(playersList));\r\n    //             console.log(\"Updated localStorage:\", localStorage.getItem(\"playerList\"));\r\n    //         } else {\r\n    //             console.log(\"Room is empty or players node deleted\");\r\n    //             console.log(\"roomId\", roomId);\r\n\r\n    //             setPlayerArray([]); // Clear state\r\n    //             localStorage.removeItem(\"playerList\"); // Clear localStorage\r\n    //         }\r\n    //     });\r\n\r\n    //     // No need to set state here; it's handled by useState initializer\r\n    //     return () => {\r\n    //         unsubscribePlayers();\r\n    //     };\r\n    // }, []);\r\n\r\n\r\n    return (\r\n        <div className=\"relative \"\r\n            style={{ zoom: \"0.75\" }}\r\n        >\r\n            {/* Ocean/Starry Night Background */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-b from-slate-900 via-blue-900 to-blue-600\">\r\n                {/* Stars overlay */}\r\n                <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,255,255,0.3)_1px,transparent_1px),radial-gradient(circle_at_75%_75%,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[length:100px_100px]\"></div>\r\n                {/* Ocean waves effect */}\r\n                <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-blue-500/50 to-transparent\"></div>\r\n                {/* Subtle animated waves */}\r\n                <div className=\"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-pulse\"></div>\r\n            </div>\r\n\r\n            {/* Content overlay */}\r\n            <div className=\"relative z-10 flex flex-col min-h-full\">\r\n                <Header isHost={isHost} spectatorCount={spectatorCount} />\r\n\r\n                <div className=\"flex flex-1 p-4 gap-4\">\r\n                    <div className=\"w-full lg:w-4/5 flex flex-col\">\r\n                        {/* Progress bar with ocean theme */}\r\n                        <div className=\"w-full h-3 bg-slate-700/50 rounded-full mb-4 border border-blue-400/30 shadow-lg\">\r\n                            <div\r\n                                className=\"h-full bg-gradient-to-r from-blue-400 to-cyan-300 rounded-full shadow-inner\"\r\n                                style={{\r\n                                    width: timeLeft > 0 ? '100%' : '100%', // Always reset to 100% width\r\n                                    animation: timeLeft > 0 ? `shrink ${roundTime[round]}s linear forwards` : 'none',\r\n                                    animationPlayState: timeLeft > 0 ? 'running' : 'paused',\r\n                                }}\r\n                                key={animationKey} // Restart animation on round change\r\n                            ></div>\r\n                        </div>\r\n\r\n                        {/* Question component with ocean-themed styling */}\r\n                        <div className={`bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4  ${isHost ? \"min-h-[400px]\" : \"min-h-[400px]\"}`}>\r\n                            {questionComponent}\r\n                        </div>\r\n\r\n                        {/* Player score with ocean theme */}\r\n                        <div className=\"bg-slate-800/70 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-xl\">\r\n                            {PlayerScore}\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"hidden lg:flex lg:w-1/5 flex-col gap-4\">\r\n                        {/* Round indicator with nautical theme */}\r\n                        {!isHost && (\r\n                            <div className=\"bg-gradient-to-r from-blue-600 to-cyan-500 text-white text-center font-bold text-lg p-4 rounded-xl shadow-xl border border-blue-400/50\">\r\n                                <div className=\"text-sm opacity-90 mb-1\">Vòng</div>\r\n                                <div className=\"text-xl\">\r\n                                    {roundTabs.find(tab => tab.key === currentRound)?.label || \"\"}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n                        {/* Sidebar with ocean theme */}\r\n                        <div className=\"bg-slate-800/70 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-xl p-4 flex-1\">\r\n                            {SideBar}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Mobile round indicator */}\r\n                {/* <div className=\"lg:hidden mx-4 mb-4\">\r\n                    <div className=\"bg-gradient-to-r from-blue-600 to-cyan-500 text-white text-center font-bold text-base p-3 rounded-xl shadow-xl border border-blue-400/50\">\r\n                        <span className=\"text-sm opacity-90\">Vòng </span>\r\n                        <span>{round ? roundName[round as keyof typeof roundName] : \"\"}</span>\r\n                    </div>\r\n                </div> */}\r\n\r\n                {/* Chat button with ocean theme */}\r\n                <button\r\n                    className=\"fixed bottom-6 right-6 bg-gradient-to-r from-blue-600 to-cyan-500 text-white w-14 h-14 flex items-center justify-center rounded-full shadow-2xl border-2 border-blue-400/50 hover:scale-110 transition-transform duration-200\"\r\n                    onClick={() => setIsChatOpen(!isChatOpen)}\r\n                >\r\n                    <span className=\"text-xl\">{isChatOpen ? \"✖\" : \"💬\"}</span>\r\n                </button>\r\n\r\n                {/* Modal with ocean theme */}\r\n                {isModalOpen && (\r\n                    <div\r\n                        className=\"fixed inset-0 bg-slate-900/90 backdrop-blur-sm flex justify-center items-center z-50\"\r\n                        onClick={() => setIsModalOpen(false)}\r\n                    >\r\n                        <img\r\n                            src=\"https://a.travel-assets.com/findyours-php/viewfinder/images/res70/474000/474240-Left-Bank-Paris.jpg\"\r\n                            alt=\"Full Size\"\r\n                            className=\"max-w-full max-h-full rounded-xl shadow-2xl\"\r\n                        />\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Rules Modal */}\r\n            <RulesModal\r\n                isOpen={showRulesModal}\r\n                onClose={() => {\r\n                    setShowRulesModal(false);\r\n                    // Don't clear Firebase data - let each client manage their own modal state\r\n                }}\r\n                round={rulesRound}\r\n                mode={mode}\r\n                roomRules={rules}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\n\r\n\r\nexport default Play;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAaC,MAAM,QAAqB,OAAO;AAClF,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,SAAS,QAAQ,0BAA0B;AAEpD,SAASC,UAAU,EAAkDC,qBAAqB,EAAkBC,eAAe,EAAgDC,iBAAiB,EAAsBC,kBAAkB,QAAQ,8BAA8B;AAC1Q,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAG/D,SAASC,OAAO,QAAQ,wBAAwB;AAGhD,OAAOC,UAAU,MAAM,0BAA0B;AAGjD,SAASC,mBAAmB,QAAQ,iBAAiB;AACrD,SAASC,YAAY,QAAQ,gCAAgC;AAI7D,OAAO,cAAc;AACrB,SAASC,cAAc,QAAwB,cAAc;AAC7D,SAASC,uBAAuB,EAAEC,qBAAqB,QAAQ,+BAA+B;AAC9F,SAASC,SAAS,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgBpD,MAAMC,IAAyB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC,MAAM,GAAG,KAAK;EAAEC,WAAW;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA;EAG/F,MAAMC,SAAS,GAAG,CACd;IAAEC,GAAG,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9B;IAAED,GAAG,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAY,CAAC,EAChC;IAAED,GAAG,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9B;IAAED,GAAG,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAa,CAAC,EACjC;IAAED,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC1C;IAAED,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAY,CAAC,CACtC;EAED,MAAMC,SAAS,GAAG;IACd,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,MAAM,EAAE;EACZ,CAAC;EAED,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,eAAe,GAAGhC,MAAM,CAAC,EAAE,CAAC;EAClC,MAAMiC,MAAM,GAAGhB,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAS,CAAC,CAAC;EAC/D,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,GAAG,CAAC;EACjD,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAACgD,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;EACpE,MAAM,CAACC,MAAM,CAAC,GAAGvC,eAAe,CAAC,CAAC;EAClC,MAAMwC,KAAK,GAAID,MAAM,CAACE,GAAG,CAAC,OAAO,CAAC,IAAuC,GAAG;EAC5E,MAAM;IAAEC,OAAO;IAAEC,UAAU;IAAEC,SAAS;IAAEC,YAAY;IAAEC,SAAS;IAAEC,YAAY;IAAEC,cAAc;IAAEC,QAAQ;IAAEC,kBAAkB;IAAEC,aAAa;IAAEC,gBAAgB;IAAEC,YAAY;IAAEC;EAAc,CAAC,GAAG7D,SAAS,CAAC,CAAC;EACzM,MAAM;IAAE8D,YAAY;IAAEC,eAAe;IAAEC,YAAY;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAG3D,OAAO,CAAC,CAAC;EAC/F,MAAM4D,SAAS,GAAGtE,MAAM,CAAC,KAAK,CAAC;EAC/B,MAAM;IAAEuE,QAAQ;IAAEC;EAAW,CAAC,GAAG3D,YAAY,CAAC,CAAC;EAG/C,MAAM,CAAC4D,YAAY,CAAC,GAAGhE,eAAe,CAAC,CAAC;EAGxC,MAAMiE,YAAY,GAAGD,YAAY,CAACvB,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EACrD,MAAMyB,QAAQ,GAAGF,YAAY,CAACvB,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG;EACpD,MAAM0B,MAAM,GAAGH,YAAY,CAACvB,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;EAE/C,MAAM;IAAC2B,uBAAuB;IAAEC;EAAqB,CAAC,GAAGlE,mBAAmB,CAACgE,MAAM,CAAC;EACpF,MAAMG,QAAQ,GAAGjE,cAAc,CAAC,CAAC;EACjC,MAAMkE,cAAc,GAAGhF,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMiF,MAAM,GAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;EACG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAlF,SAAS,CAAC,MAAK;IACX,MAAMmF,kBAAkB,GAAGJ,qBAAqB,CAAC,MAAM;MACnDX,eAAe,CAAEgB,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IACvC,CAAC,CAAC;EACN,CAAC,EAAC,EAAE,CAAC;EAELpF,SAAS,CAAC,MAAM;IACZ,MAAMqF,mBAAmB,GAAGP,uBAAuB,CAAC,MAAM;MACtD,IAAG,CAACvD,MAAM,EAAE;QACRyD,QAAQ,CAAChE,uBAAuB,CAAC,EAAE,CAAC,CAAC;MACzC;MAEAgE,QAAQ,CAAC/D,qBAAqB,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,OAAO,MAAM;MACToE,mBAAmB,CAAC,CAAC;IACzB,CAAC;EACL,CAAC,EAAC,CAACV,YAAY,CAAC,CAAC;EAEjB3E,SAAS,CAAC,MAAM;IACZ,MAAMsF,iBAAiB,GAAGhF,eAAe,CACrC,MAAM;MACF,MAAMiF,KAAK,GAAGrD,MAAM,CAAC,SAAS,CAAC;MAC/B,IAAIqD,KAAK,EAAE;QACPA,KAAK,CAACC,IAAI,CAAC,CAAC;MAChB;IACJ,CACJ,CAAC;IAED,OAAO,MAAM;MACTF,iBAAiB,CAAC,CAAC;IACvB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAGN,MAAMG,iBAAiB,GAAG,MAAOC,KAAa,IAAK;IAC/CC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEjB,YAAY,CAAC;IACzC,MAAMkB,QAAQ,GAAGC,QAAQ,CAACnB,YAAY,CAAC,GAAGe,KAAK;IAC/CC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,QAAQ,CAAC;IAClC,IAAIA,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,EAAE;MAAE;MAClC7D,QAAQ,CAAC,UAAU6D,QAAQ,aAAajB,QAAQ,WAAWC,MAAM,EAAE,CAAC;IACxE;;IAEA;IACAb,aAAa,CAAC,EAAE,CAAC;;IAEjB;IACA,MAAM5D,UAAU,CAACyE,MAAM,EAAE,WAAW,CAAC;IACrC,MAAMzE,UAAU,CAACyE,MAAM,EAAE,SAAS,CAAC;IACnC,MAAMzE,UAAU,CAACyE,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;IACzC,MAAMzE,UAAU,CAACyE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAClC,MAAMzE,UAAU,CAACyE,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC;EAC5C,CAAC;EAGD7E,SAAS,CAAC,MAAM;IACZ,IAAI,CAAC6E,MAAM,IAAI,CAAChC,MAAM,EAAE;;IAExB;IACA,MAAMkD,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAClD,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC;IAC/E,MAAMkD,kBAAkB,GAAG3F,iBAAiB,CAACsE,MAAM,EAAEhC,MAAM,EAAEkD,aAAa,CAAC;IAE3E,OAAO,MAAM;MACT;MACAG,kBAAkB,CAAC,CAAC;IACxB,CAAC;EACL,CAAC,EAAE,CAACrB,MAAM,EAAEhC,MAAM,CAAC,CAAC;EAEpB7C,SAAS,CAAC,MAAM;IACZ,MAAMmG,oBAAoB,GAAG9F,qBAAqB,CAACwE,MAAM,EAAGuB,KAAK,IAAK;MAClET,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEQ,KAAK,CAAC;MAE/B5D,iBAAiB,CAAC4D,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEF,OAAO,MAAM;MACTD,oBAAoB,CAAC,CAAC;IAC1B,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAENnG,SAAS,CAAC,MAAM;IACZ,MAAMqG,gBAAgB,GAAG7F,kBAAkB,CAACqE,MAAM,EAAGyB,IAAI,IAAK;MAC1DX,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEU,IAAI,CAAC;MACzC7C,YAAY,CAAC6C,IAAI,CAAC;;MAElB;MACA,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE;QACnB3D,aAAa,CAAC0D,IAAI,CAACpD,KAAK,CAAC;QACzBR,iBAAiB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACHA,iBAAiB,CAAC,KAAK,CAAC;MAC5B;IACJ,CAAC,CAAC;IAEF,OAAO,MAAM;MACT2D,gBAAgB,CAAC,CAAC;IACtB,CAAC;EACL,CAAC,EAAE,CAACxB,MAAM,CAAC,CAAC;;EAEZ;;EAEA,MAAM2B,MAAM,GAAGvG,MAAM,CAAC,KAAK,CAAC;EAC5B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAGA,oBACImB,OAAA;IAAKqF,SAAS,EAAC,WAAW;IACtBC,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAGxBxF,OAAA;MAAKqF,SAAS,EAAC,2EAA2E;MAAAG,QAAA,gBAEtFxF,OAAA;QAAKqF,SAAS,EAAC;MAAyM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE/N5F,OAAA;QAAKqF,SAAS,EAAC;MAAwF;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE9G5F,OAAA;QAAKqF,SAAS,EAAC;MAAsH;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3I,CAAC,eAGN5F,OAAA;MAAKqF,SAAS,EAAC,wCAAwC;MAAAG,QAAA,gBACnDxF,OAAA,CAAClB,MAAM;QAACqB,MAAM,EAAEA,MAAO;QAACgB,cAAc,EAAEA;MAAe;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1D5F,OAAA;QAAKqF,SAAS,EAAC,uBAAuB;QAAAG,QAAA,gBAClCxF,OAAA;UAAKqF,SAAS,EAAC,+BAA+B;UAAAG,QAAA,gBAE1CxF,OAAA;YAAKqF,SAAS,EAAC,kFAAkF;YAAAG,QAAA,eAC7FxF,OAAA;cACIqF,SAAS,EAAC,6EAA6E;cACvFC,KAAK,EAAE;gBACHO,KAAK,EAAEzC,QAAQ,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;gBAAE;gBACvC0C,SAAS,EAAE1C,QAAQ,GAAG,CAAC,GAAG,UAAUzC,SAAS,CAACmB,KAAK,CAAC,mBAAmB,GAAG,MAAM;gBAChFiE,kBAAkB,EAAE3C,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG;cACnD;YAAE,GACGL,YAAY;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN5F,OAAA;YAAKqF,SAAS,EAAE,8FAA8FlF,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;YAAAqF,QAAA,EACtJtF;UAAiB;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eAGN5F,OAAA;YAAKqF,SAAS,EAAC,iFAAiF;YAAAG,QAAA,EAC3FpF;UAAW;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN5F,OAAA;UAAKqF,SAAS,EAAC,wCAAwC;UAAAG,QAAA,GAElD,CAACrF,MAAM,iBACJH,OAAA;YAAKqF,SAAS,EAAC,wIAAwI;YAAAG,QAAA,gBACnJxF,OAAA;cAAKqF,SAAS,EAAC,yBAAyB;cAAAG,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnD5F,OAAA;cAAKqF,SAAS,EAAC,SAAS;cAAAG,QAAA,EACnB,EAAAjF,eAAA,GAAAC,SAAS,CAACwF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACxF,GAAG,KAAK8C,YAAY,CAAC,cAAAhD,eAAA,uBAA/CA,eAAA,CAAiDG,KAAK,KAAI;YAAE;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAED5F,OAAA;YAAKqF,SAAS,EAAC,4FAA4F;YAAAG,QAAA,EACtGnF;UAAO;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAWN5F,OAAA;QACIqF,SAAS,EAAC,+NAA+N;QACzOa,OAAO,EAAEA,CAAA,KAAMlF,aAAa,CAAC,CAACD,UAAU,CAAE;QAAAyE,QAAA,eAE1CxF,OAAA;UAAMqF,SAAS,EAAC,SAAS;UAAAG,QAAA,EAAEzE,UAAU,GAAG,GAAG,GAAG;QAAI;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,EAGR3E,WAAW,iBACRjB,OAAA;QACIqF,SAAS,EAAC,sFAAsF;QAChGa,OAAO,EAAEA,CAAA,KAAMhF,cAAc,CAAC,KAAK,CAAE;QAAAsE,QAAA,eAErCxF,OAAA;UACImG,GAAG,EAAC,qGAAqG;UACzGC,GAAG,EAAC,WAAW;UACff,SAAS,EAAC;QAA6C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGN5F,OAAA,CAACR,UAAU;MACP6G,MAAM,EAAEhF,cAAe;MACvBiF,OAAO,EAAEA,CAAA,KAAM;QACXhF,iBAAiB,CAAC,KAAK,CAAC;QACxB;MACJ,CAAE;MACFQ,KAAK,EAAEP,UAAW;MAClB0B,IAAI,EAAEA,IAAK;MACXb,SAAS,EAAEc;IAAM;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACtF,EAAA,CA3VIL,IAAyB;EAAA,QAoBVZ,WAAW,EAEbS,SAAS,EAOPR,eAAe,EAE8JP,SAAS,EACjHQ,OAAO,EAE5DG,YAAY,EAGtBJ,eAAe,EAOmBG,mBAAmB,EAC3DE,cAAc;AAAA;AAAA4G,EAAA,GA7C7BtG,IAAyB;AA+V/B,eAAeA,IAAI;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}