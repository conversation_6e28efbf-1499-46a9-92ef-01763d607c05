{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\SimpleColorPicker.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst COLORS = [{\n  name: 'Đỏ',\n  value: '#FF4444'\n}, {\n  name: 'Xanh lá',\n  value: '#44FF44'\n}, {\n  name: 'Xanh dương',\n  value: '#4444FF'\n}, {\n  name: 'Vàng',\n  value: '#FFFF44'\n}, {\n  name: 'Tím',\n  value: '#FF44FF'\n}, {\n  name: 'Cam',\n  value: '#FF8844'\n}, {\n  name: 'Hồng',\n  value: '#FF88CC'\n}, {\n  name: 'Xanh lam',\n  value: '#44FFFF'\n}];\nconst SimpleColorPicker = ({\n  playerStt,\n  currentColor,\n  onColorChange,\n  usedColors\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const handleColorSelect = color => {\n    onColorChange(playerStt, color);\n    setIsOpen(false);\n  };\n  const handleRemoveColor = () => {\n    onColorChange(playerStt, '');\n    setIsOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsOpen(!isOpen),\n      className: \"w-6 h-6 rounded-full border-2 border-white shadow-sm hover:scale-110 transition-transform\",\n      style: {\n        backgroundColor: currentColor || '#666666',\n        opacity: currentColor ? 1 : 0.5\n      },\n      title: currentColor ? 'Thay đổi màu' : 'Chọn màu'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 z-40\",\n        onClick: () => setIsOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-8 left-0 z-50 bg-slate-800 border border-slate-600 rounded-lg shadow-xl p-2 min-w-[200px]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white text-xs mb-2 font-medium\",\n          children: \"Ch\\u1ECDn m\\xE0u:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-4 gap-1 mb-2\",\n          children: COLORS.map(color => {\n            const isUsed = usedColors.has(color.value);\n            const isSelected = currentColor === color.value;\n            const isDisabled = isUsed && !isSelected;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => !isDisabled && handleColorSelect(color.value),\n              disabled: isDisabled,\n              className: `\n                      w-8 h-8 rounded border-2 transition-all\n                      ${isSelected ? 'border-white scale-110' : isDisabled ? 'border-gray-600 opacity-50 cursor-not-allowed' : 'border-gray-400 hover:border-white hover:scale-105'}\n                    `,\n              style: {\n                backgroundColor: color.value\n              },\n              title: isDisabled ? `${color.name} đã được sử dụng` : color.name,\n              children: isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-white rounded-full mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 23\n              }, this)\n            }, color.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this), currentColor && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRemoveColor,\n          className: \"w-full text-xs text-red-400 hover:text-red-300 py-1 border-t border-slate-600 mt-1\",\n          children: \"X\\xF3a m\\xE0u\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleColorPicker, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = SimpleColorPicker;\nexport default SimpleColorPicker;\nvar _c;\n$RefreshReg$(_c, \"SimpleColorPicker\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "COLORS", "name", "value", "SimpleColorPicker", "player<PERSON>tt", "currentColor", "onColorChange", "usedColors", "_s", "isOpen", "setIsOpen", "handleColorSelect", "color", "handleRemoveColor", "className", "children", "onClick", "style", "backgroundColor", "opacity", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "isUsed", "has", "isSelected", "isDisabled", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/SimpleColorPicker.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\ninterface SimpleColorPickerProps {\n  playerStt: string;\n  currentColor?: string;\n  onColorChange: (playerStt: string, color: string) => void;\n  usedColors: Set<string>;\n}\n\nconst COLORS = [\n  { name: 'Đỏ', value: '#FF4444' },\n  { name: '<PERSON>anh lá', value: '#44FF44' },\n  { name: '<PERSON>anh dương', value: '#4444FF' },\n  { name: 'Vàng', value: '#FFFF44' },\n  { name: 'Tím', value: '#FF44FF' },\n  { name: '<PERSON>', value: '#FF8844' },\n  { name: 'Hồng', value: '#FF88CC' },\n  { name: 'Xanh lam', value: '#44FFFF' },\n];\n\nconst SimpleColorPicker: React.FC<SimpleColorPickerProps> = ({\n  playerStt,\n  currentColor,\n  onColorChange,\n  usedColors\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const handleColorSelect = (color: string) => {\n    onColorChange(playerStt, color);\n    setIsOpen(false);\n  };\n\n  const handleRemoveColor = () => {\n    onColorChange(playerStt, '');\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      {/* Color display button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"w-6 h-6 rounded-full border-2 border-white shadow-sm hover:scale-110 transition-transform\"\n        style={{ \n          backgroundColor: currentColor || '#666666',\n          opacity: currentColor ? 1 : 0.5\n        }}\n        title={currentColor ? 'Thay đổi màu' : 'Chọn màu'}\n      />\n\n      {/* Color picker dropdown */}\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 z-40\" \n            onClick={() => setIsOpen(false)}\n          />\n          \n          {/* Dropdown */}\n          <div className=\"absolute top-8 left-0 z-50 bg-slate-800 border border-slate-600 rounded-lg shadow-xl p-2 min-w-[200px]\">\n            <div className=\"text-white text-xs mb-2 font-medium\">Chọn màu:</div>\n            \n            {/* Color grid */}\n            <div className=\"grid grid-cols-4 gap-1 mb-2\">\n              {COLORS.map((color) => {\n                const isUsed = usedColors.has(color.value);\n                const isSelected = currentColor === color.value;\n                const isDisabled = isUsed && !isSelected;\n                \n                return (\n                  <button\n                    key={color.value}\n                    onClick={() => !isDisabled && handleColorSelect(color.value)}\n                    disabled={isDisabled}\n                    className={`\n                      w-8 h-8 rounded border-2 transition-all\n                      ${isSelected \n                        ? 'border-white scale-110' \n                        : isDisabled \n                          ? 'border-gray-600 opacity-50 cursor-not-allowed' \n                          : 'border-gray-400 hover:border-white hover:scale-105'\n                      }\n                    `}\n                    style={{ backgroundColor: color.value }}\n                    title={isDisabled ? `${color.name} đã được sử dụng` : color.name}\n                  >\n                    {isSelected && (\n                      <div className=\"w-2 h-2 bg-white rounded-full mx-auto\" />\n                    )}\n                  </button>\n                );\n              })}\n            </div>\n            \n            {/* Remove color button */}\n            {currentColor && (\n              <button\n                onClick={handleRemoveColor}\n                className=\"w-full text-xs text-red-400 hover:text-red-300 py-1 border-t border-slate-600 mt-1\"\n              >\n                Xóa màu\n              </button>\n            )}\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default SimpleColorPicker;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASxC,MAAMC,MAAM,GAAG,CACb;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,IAAI,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAU,CAAC,EACrC;EAAED,IAAI,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAU,CAAC,EACxC;EAAED,IAAI,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAU,CAAC,EAClC;EAAED,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,IAAI,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAU,CAAC,EAClC;EAAED,IAAI,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAU,CAAC,CACvC;AAED,MAAMC,iBAAmD,GAAGA,CAAC;EAC3DC,SAAS;EACTC,YAAY;EACZC,aAAa;EACbC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAE3C,MAAMgB,iBAAiB,GAAIC,KAAa,IAAK;IAC3CN,aAAa,CAACF,SAAS,EAAEQ,KAAK,CAAC;IAC/BF,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BP,aAAa,CAACF,SAAS,EAAE,EAAE,CAAC;IAC5BM,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,oBACEb,OAAA;IAAKiB,SAAS,EAAC,UAAU;IAAAC,QAAA,gBAEvBlB,OAAA;MACEmB,OAAO,EAAEA,CAAA,KAAMN,SAAS,CAAC,CAACD,MAAM,CAAE;MAClCK,SAAS,EAAC,2FAA2F;MACrGG,KAAK,EAAE;QACLC,eAAe,EAAEb,YAAY,IAAI,SAAS;QAC1Cc,OAAO,EAAEd,YAAY,GAAG,CAAC,GAAG;MAC9B,CAAE;MACFe,KAAK,EAAEf,YAAY,GAAG,cAAc,GAAG;IAAW;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,EAGDf,MAAM,iBACLZ,OAAA,CAAAE,SAAA;MAAAgB,QAAA,gBAEElB,OAAA;QACEiB,SAAS,EAAC,oBAAoB;QAC9BE,OAAO,EAAEA,CAAA,KAAMN,SAAS,CAAC,KAAK;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAGF3B,OAAA;QAAKiB,SAAS,EAAC,wGAAwG;QAAAC,QAAA,gBACrHlB,OAAA;UAAKiB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAGpE3B,OAAA;UAAKiB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EACzCf,MAAM,CAACyB,GAAG,CAAEb,KAAK,IAAK;YACrB,MAAMc,MAAM,GAAGnB,UAAU,CAACoB,GAAG,CAACf,KAAK,CAACV,KAAK,CAAC;YAC1C,MAAM0B,UAAU,GAAGvB,YAAY,KAAKO,KAAK,CAACV,KAAK;YAC/C,MAAM2B,UAAU,GAAGH,MAAM,IAAI,CAACE,UAAU;YAExC,oBACE/B,OAAA;cAEEmB,OAAO,EAAEA,CAAA,KAAM,CAACa,UAAU,IAAIlB,iBAAiB,CAACC,KAAK,CAACV,KAAK,CAAE;cAC7D4B,QAAQ,EAAED,UAAW;cACrBf,SAAS,EAAE;AAC/B;AACA,wBAAwBc,UAAU,GACR,wBAAwB,GACxBC,UAAU,GACR,+CAA+C,GAC/C,oDAAoD;AAChF,qBACsB;cACFZ,KAAK,EAAE;gBAAEC,eAAe,EAAEN,KAAK,CAACV;cAAM,CAAE;cACxCkB,KAAK,EAAES,UAAU,GAAG,GAAGjB,KAAK,CAACX,IAAI,kBAAkB,GAAGW,KAAK,CAACX,IAAK;cAAAc,QAAA,EAEhEa,UAAU,iBACT/B,OAAA;gBAAKiB,SAAS,EAAC;cAAuC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACzD,GAjBIZ,KAAK,CAACV,KAAK;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBV,CAAC;UAEb,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGLnB,YAAY,iBACXR,OAAA;UACEmB,OAAO,EAAEH,iBAAkB;UAC3BC,SAAS,EAAC,oFAAoF;UAAAC,QAAA,EAC/F;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA,eACN,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChB,EAAA,CA1FIL,iBAAmD;AAAA4B,EAAA,GAAnD5B,iBAAmD;AA4FzD,eAAeA,iBAAiB;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}