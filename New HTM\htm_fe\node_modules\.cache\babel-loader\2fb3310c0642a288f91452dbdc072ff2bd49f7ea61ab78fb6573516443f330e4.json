{"ast": null, "code": "var _s = $RefreshSig$();\n// Firebase real-time listener hook\nimport { useEffect, useCallback } from 'react';\nimport { useAppDispatch } from '../../../app/store';\nimport { setPlayers, setCurrentQuestion, setScores, setRound2Grid, setRound4Grid, setIsInputDisabled, setCurrentCorrectAnswer } from '../../../app/store/slices/gameSlice';\nimport { setCurrentRoom, setPlayers as setRoomPlayers } from '../../../app/store/slices/roomSlice';\nimport { firebaseRealtimeService } from '../../services/firebase/realtime';\nimport { FirebaseRoomListener } from '../../../services/firebaseServices';\nexport const useFirebaseListener = roomId => {\n  _s();\n  const dispatch = useAppDispatch();\n  const listener = FirebaseRoomListener.getInstance(roomId || '');\n  /**\r\n   * Listen to room data changes\r\n   */\n  const listenToRoom = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRoom(roomId, data => {\n      if (data) {\n        // Update room state\n        dispatch(setCurrentRoom(data));\n\n        // Call optional callback\n        callback === null || callback === void 0 ? void 0 : callback(data);\n      }\n    });\n  }, [roomId, dispatch]);\n  const listenToTimeStart = useCallback(callback => {\n    if (!roomId) return () => {};\n    dispatch(setIsInputDisabled(false));\n    return listener.listenToTimeStart(callback);\n  }, [roomId, dispatch]);\n  const listenToSound = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToSound(callback);\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to player answers\r\n   */\n  const listenToNewPlayer = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToPlayers(players => {\n      dispatch(setPlayers(players));\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to player answers\r\n   */\n  const listenToPlayerAnswers = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToPlayerAnswers(roomId, answers => {\n      // Convert to array and update Redux state\n      const playersArray = Object.values(answers);\n      dispatch(setPlayers(playersArray));\n      dispatch(setRoomPlayers(playersArray.map(p => ({\n        ...p,\n        joinedAt: '',\n        isReady: true,\n        isConnected: true,\n        role: 'player'\n      }))));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(answers);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to current question\r\n   */\n  const listenToCurrentQuestion = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToQuestion(question => {\n      dispatch(setCurrentQuestion(question));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to correct answer\r\n   */\n  const listenToCorrectAnswer = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToCorrectAnswer(answer => {\n      dispatch(setCurrentCorrectAnswer(answer));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback();\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to scores\r\n   */\n  const listenToScores = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToScores(roomId, scores => {\n      dispatch(setScores(scores));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(scores);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to game state\r\n   */\n  const listenToGameState = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToGameState(roomId, state => {\n      if (state) {\n        // Update relevant Redux state based on game state\n        if (state.currentRound) {\n          // dispatch(setCurrentRound(state.currentRound));\n        }\n        if (state.isActive !== undefined) {\n          // dispatch(setIsActive(state.isActive));\n        }\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(state);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to Round 2 grid\r\n   */\n  const listenToRound2Grid = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRound2Grid(roomId, grid => {\n      if (grid) {\n        dispatch(setRound2Grid(grid));\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(grid);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to Round 4 grid\r\n   */\n  const listenToRound4Grid = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRound4Grid(roomId, grid => {\n      if (grid) {\n        dispatch(setRound4Grid(grid));\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(grid);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Setup all listeners at once\r\n   */\n  const setupAllListeners = useCallback(callbacks => {\n    if (!roomId) return () => {};\n    const unsubscribers = [listenToRoom(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRoomChange), listenToPlayerAnswers(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onPlayerAnswersChange), listenToCurrentQuestion(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onQuestionChange), listenToScores(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onScoresChange), listenToGameState(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onGameStateChange), listenToRound2Grid(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRound2GridChange), listenToRound4Grid(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRound4GridChange)];\n    return () => {\n      unsubscribers.forEach(unsubscribe => unsubscribe());\n    };\n  }, [roomId, listenToRoom, listenToPlayerAnswers, listenToCurrentQuestion, listenToScores, listenToGameState, listenToRound2Grid, listenToRound4Grid]);\n\n  /**\r\n   * Update player data\r\n   */\n  const updatePlayer = useCallback(async (playerId, playerData) => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updatePlayer(roomId, playerId, playerData);\n  }, [roomId]);\n\n  /**\r\n   * Set current question\r\n   */\n  const setCurrentQuestionFirebase = useCallback(async question => {\n    if (!roomId) return;\n    await firebaseRealtimeService.setCurrentQuestion(roomId, question);\n  }, [roomId]);\n\n  /**\r\n   * Update scores\r\n   */\n  const updateScoresFirebase = useCallback(async scores => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updateScores(roomId, scores);\n  }, [roomId]);\n\n  /**\r\n   * Update game state\r\n   */\n  const updateGameStateFirebase = useCallback(async gameState => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updateGameState(roomId, gameState);\n  }, [roomId]);\n\n  /**\r\n   * Setup on disconnect\r\n   */\n  const setupDisconnect = useCallback(async (uid, úcallback) => {\n    if (!roomId) return;\n    return listener.setupOnDisconnect(roomId, gameState);\n  }, [roomId]);\n  const deletePath = async path => {\n    await listener.deletePath(path);\n  };\n\n  /**\r\n   * Cleanup all listeners on unmount\r\n   */\n  useEffect(() => {\n    return () => {\n      firebaseRealtimeService.removeAllListeners();\n    };\n  }, []);\n  return {\n    // Listeners\n    listenToRoom,\n    listenToNewPlayer,\n    listenToPlayerAnswers,\n    listenToCorrectAnswer,\n    listenToCurrentQuestion,\n    listenToScores,\n    listenToGameState,\n    listenToRound2Grid,\n    listenToRound4Grid,\n    listenToTimeStart,\n    listenToSound,\n    setupAllListeners,\n    // Writers\n    updatePlayer,\n    setCurrentQuestionFirebase,\n    updateScoresFirebase,\n    updateGameStateFirebase,\n    //Delete\n    deletePath\n  };\n};\n_s(useFirebaseListener, \"wGSSXAPQ9jxnwqO1GJA6yZmQPV4=\", false, function () {\n  return [useAppDispatch];\n});\nexport default useFirebaseListener;", "map": {"version": 3, "names": ["useEffect", "useCallback", "useAppDispatch", "setPlayers", "setCurrentQuestion", "setScores", "setRound2Grid", "setRound4Grid", "setIsInputDisabled", "setCurrentCorrectAnswer", "setCurrentRoom", "setRoomPlayers", "firebaseRealtimeService", "FirebaseRoomListener", "useFirebaseListener", "roomId", "_s", "dispatch", "listener", "getInstance", "listenToRoom", "callback", "data", "listenToTimeStart", "listenToSound", "listenToNewPlayer", "listenToPlayers", "players", "listenToPlayerAnswers", "answers", "players<PERSON><PERSON>y", "Object", "values", "map", "p", "joinedAt", "isReady", "isConnected", "role", "listenToCurrentQuestion", "listenToQuestion", "question", "listenToCorrectAnswer", "answer", "listenToScores", "scores", "listenToGameState", "state", "currentRound", "isActive", "undefined", "listenToRound2Grid", "grid", "listenToRound4Grid", "setupAllListeners", "callbacks", "unsubscribers", "onRoomChange", "onPlayerAnswersChange", "onQuestionChange", "onScoresChange", "onGameStateChange", "onRound2GridChange", "onRound4GridChange", "for<PERSON>ach", "unsubscribe", "updatePlayer", "playerId", "player<PERSON><PERSON>", "setCurrentQuestionFirebase", "updateScoresFirebase", "updateScores", "updateGameStateFirebase", "gameState", "updateGameState", "setupDisconnect", "uid", "<PERSON><PERSON><PERSON><PERSON>", "setupOnDisconnect", "deletePath", "path", "removeAllListeners"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/hooks/firebase/useFirebaseListener.ts"], "sourcesContent": ["// Firebase real-time listener hook\r\nimport { useEffect, useCallback } from 'react';\r\nimport { useAppDispatch } from '../../../app/store';\r\nimport {\r\n  setPlayers,\r\n  setCurrentQuestion,\r\n  setScores,\r\n  setRound2Grid,\r\n  setRound4Grid,\r\n  setIsInputDisabled,\r\n  setCurrentCorrectAnswer\r\n} from '../../../app/store/slices/gameSlice';\r\nimport {\r\n  setCurrentRoom,\r\n  setPlayers as setRoomPlayers\r\n} from '../../../app/store/slices/roomSlice';\r\nimport { firebaseRealtimeService } from '../../services/firebase/realtime';\r\nimport { PlayerData, Question, Score, Room } from '../../types';\r\nimport { FirebaseRoomListener } from '../../../services/firebaseServices';\r\n\r\nexport const useFirebaseListener = (roomId: string | null) => {\r\n  const dispatch = useAppDispatch();\r\n  const listener = FirebaseRoomListener.getInstance(roomId || '');\r\n  /**\r\n   * Listen to room data changes\r\n   */\r\n  const listenToRoom = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToRoom(roomId, (data) => {\r\n      if (data) {\r\n        // Update room state\r\n        dispatch(setCurrentRoom(data as Room));\r\n\r\n        // Call optional callback\r\n        callback?.(data);\r\n      }\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  const listenToTimeStart = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n    dispatch(setIsInputDisabled(false))\r\n\r\n    return listener.listenToTimeStart(\r\n      callback\r\n    );\r\n  }, [roomId, dispatch]);\r\n\r\n  const listenToSound = useCallback((callback: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToSound(\r\n      callback\r\n    );\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to player answers\r\n   */\r\n  const listenToNewPlayer = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToPlayers((players)=> {\r\n      dispatch(setPlayers(players))\r\n\r\n      callback?.()\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to player answers\r\n   */\r\n  const listenToPlayerAnswers = useCallback((callback?: (answers: Record<string, PlayerData>) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToPlayerAnswers(roomId, (answers) => {\r\n      // Convert to array and update Redux state\r\n      const playersArray = Object.values(answers);\r\n      dispatch(setPlayers(playersArray));\r\n      dispatch(setRoomPlayers(playersArray.map(p => ({ ...p, joinedAt: '', isReady: true, isConnected: true, role: 'player' as const }))));\r\n\r\n      // Call optional callback\r\n      callback?.(answers);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to current question\r\n   */\r\n  const listenToCurrentQuestion = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToQuestion((question) => {\r\n      dispatch(setCurrentQuestion(question));\r\n\r\n      // Call optional callback\r\n      callback?.();\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to correct answer\r\n   */\r\n  const listenToCorrectAnswer = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return listener.listenToCorrectAnswer((answer) => {\r\n      dispatch(setCurrentCorrectAnswer(answer));\r\n\r\n      // Call optional callback\r\n      callback?.();\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to scores\r\n   */\r\n  const listenToScores = useCallback((callback?: (scores: Score[]) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToScores(roomId, (scores) => {\r\n      dispatch(setScores(scores));\r\n\r\n      // Call optional callback\r\n      callback?.(scores);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to game state\r\n   */\r\n  const listenToGameState = useCallback((callback?: (state: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToGameState(roomId, (state) => {\r\n      if (state) {\r\n        // Update relevant Redux state based on game state\r\n        if (state.currentRound) {\r\n          // dispatch(setCurrentRound(state.currentRound));\r\n        }\r\n        if (state.isActive !== undefined) {\r\n          // dispatch(setIsActive(state.isActive));\r\n        }\r\n      }\r\n\r\n      // Call optional callback\r\n      callback?.(state);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to Round 2 grid\r\n   */\r\n  const listenToRound2Grid = useCallback((callback?: (grid: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToRound2Grid(roomId, (grid) => {\r\n      if (grid) {\r\n        dispatch(setRound2Grid(grid));\r\n      }\r\n\r\n      // Call optional callback\r\n      callback?.(grid);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to Round 4 grid\r\n   */\r\n  const listenToRound4Grid = useCallback((callback?: (grid: any) => void) => {\r\n    if (!roomId) return () => { };\r\n\r\n    return firebaseRealtimeService.listenToRound4Grid(roomId, (grid) => {\r\n      if (grid) {\r\n        dispatch(setRound4Grid(grid));\r\n      }\r\n\r\n      // Call optional callback\r\n      callback?.(grid);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Setup all listeners at once\r\n   */\r\n  const setupAllListeners = useCallback((callbacks?: {\r\n    onRoomChange?: (data: any) => void;\r\n    onPlayerAnswersChange?: (answers: Record<string, PlayerData>) => void;\r\n    onQuestionChange?: () => void;\r\n    onScoresChange?: (scores: Score[]) => void;\r\n    onGameStateChange?: (state: any) => void;\r\n    onRound2GridChange?: (grid: any) => void;\r\n    onRound4GridChange?: (grid: any) => void;\r\n  }) => {\r\n    if (!roomId) return () => { };\r\n\r\n    const unsubscribers = [\r\n      listenToRoom(callbacks?.onRoomChange),\r\n      listenToPlayerAnswers(callbacks?.onPlayerAnswersChange),\r\n      listenToCurrentQuestion(callbacks?.onQuestionChange),\r\n      listenToScores(callbacks?.onScoresChange),\r\n      listenToGameState(callbacks?.onGameStateChange),\r\n      listenToRound2Grid(callbacks?.onRound2GridChange),\r\n      listenToRound4Grid(callbacks?.onRound4GridChange),\r\n    ];\r\n\r\n    return () => {\r\n      unsubscribers.forEach(unsubscribe => unsubscribe());\r\n    };\r\n  }, [\r\n    roomId,\r\n    listenToRoom,\r\n    listenToPlayerAnswers,\r\n    listenToCurrentQuestion,\r\n    listenToScores,\r\n    listenToGameState,\r\n    listenToRound2Grid,\r\n    listenToRound4Grid,\r\n  ]);\r\n\r\n  /**\r\n   * Update player data\r\n   */\r\n  const updatePlayer = useCallback(async (playerId: string, playerData: Partial<PlayerData>) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.updatePlayer(roomId, playerId, playerData);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Set current question\r\n   */\r\n  const setCurrentQuestionFirebase = useCallback(async (question: Question) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.setCurrentQuestion(roomId, question);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Update scores\r\n   */\r\n  const updateScoresFirebase = useCallback(async (scores: Score[]) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.updateScores(roomId, scores);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Update game state\r\n   */\r\n  const updateGameStateFirebase = useCallback(async (gameState: any) => {\r\n    if (!roomId) return;\r\n\r\n    await firebaseRealtimeService.updateGameState(roomId, gameState);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Setup on disconnect\r\n   */\r\n  const setupDisconnect = useCallback(async (uid: string, úcallback?: () => void) => {\r\n    if (!roomId) return;\r\n\r\n    return listener.setupOnDisconnect(roomId, gameState);\r\n  }, [roomId]);\r\n\r\n  const deletePath = async (path: string): Promise<void> => {\r\n    await listener.deletePath(path);\r\n  }\r\n\r\n  /**\r\n   * Cleanup all listeners on unmount\r\n   */\r\n  useEffect(() => {\r\n    return () => {\r\n      firebaseRealtimeService.removeAllListeners();\r\n    };\r\n  }, []);\r\n\r\n  return {\r\n    // Listeners\r\n    listenToRoom,\r\n    listenToNewPlayer,\r\n    listenToPlayerAnswers,\r\n    listenToCorrectAnswer,\r\n    listenToCurrentQuestion,\r\n    listenToScores,\r\n    listenToGameState,\r\n    listenToRound2Grid,\r\n    listenToRound4Grid,\r\n    listenToTimeStart,\r\n    listenToSound,\r\n    setupAllListeners,\r\n\r\n    // Writers\r\n    updatePlayer,\r\n    setCurrentQuestionFirebase,\r\n    updateScoresFirebase,\r\n    updateGameStateFirebase,\r\n\r\n    //Delete\r\n    deletePath\r\n  };\r\n};\r\n\r\nexport default useFirebaseListener;\r\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SACEC,UAAU,EACVC,kBAAkB,EAClBC,SAAS,EACTC,aAAa,EACbC,aAAa,EACbC,kBAAkB,EAClBC,uBAAuB,QAClB,qCAAqC;AAC5C,SACEC,cAAc,EACdP,UAAU,IAAIQ,cAAc,QACvB,qCAAqC;AAC5C,SAASC,uBAAuB,QAAQ,kCAAkC;AAE1E,SAASC,oBAAoB,QAAQ,oCAAoC;AAEzE,OAAO,MAAMC,mBAAmB,GAAIC,MAAqB,IAAK;EAAAC,EAAA;EAC5D,MAAMC,QAAQ,GAAGf,cAAc,CAAC,CAAC;EACjC,MAAMgB,QAAQ,GAAGL,oBAAoB,CAACM,WAAW,CAACJ,MAAM,IAAI,EAAE,CAAC;EAC/D;AACF;AACA;EACE,MAAMK,YAAY,GAAGnB,WAAW,CAAEoB,QAA8B,IAAK;IACnE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACQ,YAAY,CAACL,MAAM,EAAGO,IAAI,IAAK;MAC5D,IAAIA,IAAI,EAAE;QACR;QACAL,QAAQ,CAACP,cAAc,CAACY,IAAY,CAAC,CAAC;;QAEtC;QACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAEtB,MAAMM,iBAAiB,GAAGtB,WAAW,CAAEoB,QAAqB,IAAK;IAC/D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAC7BE,QAAQ,CAACT,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAEnC,OAAOU,QAAQ,CAACK,iBAAiB,CAC/BF,QACF,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAEtB,MAAMO,aAAa,GAAGvB,WAAW,CAAEoB,QAAoB,IAAK;IAC1D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACM,aAAa,CAC3BH,QACF,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMQ,iBAAiB,GAAGxB,WAAW,CAAEoB,QAAqB,IAAK;IAC/D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACQ,eAAe,CAAEC,OAAO,IAAI;MAC1CV,QAAQ,CAACd,UAAU,CAACwB,OAAO,CAAC,CAAC;MAE7BN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMW,qBAAqB,GAAG3B,WAAW,CAAEoB,QAAwD,IAAK;IACtG,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACgB,qBAAqB,CAACb,MAAM,EAAGc,OAAO,IAAK;MACxE;MACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACH,OAAO,CAAC;MAC3CZ,QAAQ,CAACd,UAAU,CAAC2B,YAAY,CAAC,CAAC;MAClCb,QAAQ,CAACN,cAAc,CAACmB,YAAY,CAACG,GAAG,CAACC,CAAC,KAAK;QAAE,GAAGA,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE,IAAI;QAAEC,WAAW,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpI;MACAjB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGQ,OAAO,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACd,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMsB,uBAAuB,GAAGtC,WAAW,CAAEoB,QAAqB,IAAK;IACrE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACsB,gBAAgB,CAAEC,QAAQ,IAAK;MAC7CxB,QAAQ,CAACb,kBAAkB,CAACqC,QAAQ,CAAC,CAAC;;MAEtC;MACApB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMyB,qBAAqB,GAAGzC,WAAW,CAAEoB,QAAqB,IAAK;IACnE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOG,QAAQ,CAACwB,qBAAqB,CAAEC,MAAM,IAAK;MAChD1B,QAAQ,CAACR,uBAAuB,CAACkC,MAAM,CAAC,CAAC;;MAEzC;MACAtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM2B,cAAc,GAAG3C,WAAW,CAAEoB,QAAoC,IAAK;IAC3E,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACgC,cAAc,CAAC7B,MAAM,EAAG8B,MAAM,IAAK;MAChE5B,QAAQ,CAACZ,SAAS,CAACwC,MAAM,CAAC,CAAC;;MAE3B;MACAxB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGwB,MAAM,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9B,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM6B,iBAAiB,GAAG7C,WAAW,CAAEoB,QAA+B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACkC,iBAAiB,CAAC/B,MAAM,EAAGgC,KAAK,IAAK;MAClE,IAAIA,KAAK,EAAE;QACT;QACA,IAAIA,KAAK,CAACC,YAAY,EAAE;UACtB;QAAA;QAEF,IAAID,KAAK,CAACE,QAAQ,KAAKC,SAAS,EAAE;UAChC;QAAA;MAEJ;;MAEA;MACA7B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG0B,KAAK,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChC,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMkC,kBAAkB,GAAGlD,WAAW,CAAEoB,QAA8B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACuC,kBAAkB,CAACpC,MAAM,EAAGqC,IAAI,IAAK;MAClE,IAAIA,IAAI,EAAE;QACRnC,QAAQ,CAACX,aAAa,CAAC8C,IAAI,CAAC,CAAC;MAC/B;;MAEA;MACA/B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG+B,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrC,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMoC,kBAAkB,GAAGpD,WAAW,CAAEoB,QAA8B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,OAAOH,uBAAuB,CAACyC,kBAAkB,CAACtC,MAAM,EAAGqC,IAAI,IAAK;MAClE,IAAIA,IAAI,EAAE;QACRnC,QAAQ,CAACV,aAAa,CAAC6C,IAAI,CAAC,CAAC;MAC/B;;MAEA;MACA/B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG+B,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrC,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMqC,iBAAiB,GAAGrD,WAAW,CAAEsD,SAQtC,IAAK;IACJ,IAAI,CAACxC,MAAM,EAAE,OAAO,MAAM,CAAE,CAAC;IAE7B,MAAMyC,aAAa,GAAG,CACpBpC,YAAY,CAACmC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,YAAY,CAAC,EACrC7B,qBAAqB,CAAC2B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,qBAAqB,CAAC,EACvDnB,uBAAuB,CAACgB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEI,gBAAgB,CAAC,EACpDf,cAAc,CAACW,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,cAAc,CAAC,EACzCd,iBAAiB,CAACS,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEM,iBAAiB,CAAC,EAC/CV,kBAAkB,CAACI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEO,kBAAkB,CAAC,EACjDT,kBAAkB,CAACE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEQ,kBAAkB,CAAC,CAClD;IAED,OAAO,MAAM;MACXP,aAAa,CAACQ,OAAO,CAACC,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,CACDlD,MAAM,EACNK,YAAY,EACZQ,qBAAqB,EACrBW,uBAAuB,EACvBK,cAAc,EACdE,iBAAiB,EACjBK,kBAAkB,EAClBE,kBAAkB,CACnB,CAAC;;EAEF;AACF;AACA;EACE,MAAMa,YAAY,GAAGjE,WAAW,CAAC,OAAOkE,QAAgB,EAAEC,UAA+B,KAAK;IAC5F,IAAI,CAACrD,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACsD,YAAY,CAACnD,MAAM,EAAEoD,QAAQ,EAAEC,UAAU,CAAC;EAC1E,CAAC,EAAE,CAACrD,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMsD,0BAA0B,GAAGpE,WAAW,CAAC,MAAOwC,QAAkB,IAAK;IAC3E,IAAI,CAAC1B,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACR,kBAAkB,CAACW,MAAM,EAAE0B,QAAQ,CAAC;EACpE,CAAC,EAAE,CAAC1B,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMuD,oBAAoB,GAAGrE,WAAW,CAAC,MAAO4C,MAAe,IAAK;IAClE,IAAI,CAAC9B,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAAC2D,YAAY,CAACxD,MAAM,EAAE8B,MAAM,CAAC;EAC5D,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMyD,uBAAuB,GAAGvE,WAAW,CAAC,MAAOwE,SAAc,IAAK;IACpE,IAAI,CAAC1D,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAAC8D,eAAe,CAAC3D,MAAM,EAAE0D,SAAS,CAAC;EAClE,CAAC,EAAE,CAAC1D,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAM4D,eAAe,GAAG1E,WAAW,CAAC,OAAO2E,GAAW,EAAEC,SAAsB,KAAK;IACjF,IAAI,CAAC9D,MAAM,EAAE;IAEb,OAAOG,QAAQ,CAAC4D,iBAAiB,CAAC/D,MAAM,EAAE0D,SAAS,CAAC;EACtD,CAAC,EAAE,CAAC1D,MAAM,CAAC,CAAC;EAEZ,MAAMgE,UAAU,GAAG,MAAOC,IAAY,IAAoB;IACxD,MAAM9D,QAAQ,CAAC6D,UAAU,CAACC,IAAI,CAAC;EACjC,CAAC;;EAED;AACF;AACA;EACEhF,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXY,uBAAuB,CAACqE,kBAAkB,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL;IACA7D,YAAY;IACZK,iBAAiB;IACjBG,qBAAqB;IACrBc,qBAAqB;IACrBH,uBAAuB;IACvBK,cAAc;IACdE,iBAAiB;IACjBK,kBAAkB;IAClBE,kBAAkB;IAClB9B,iBAAiB;IACjBC,aAAa;IACb8B,iBAAiB;IAEjB;IACAY,YAAY;IACZG,0BAA0B;IAC1BC,oBAAoB;IACpBE,uBAAuB;IAEvB;IACAO;EACF,CAAC;AACH,CAAC;AAAC/D,EAAA,CA3RWF,mBAAmB;EAAA,QACbZ,cAAc;AAAA;AA4RjC,eAAeY,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}