{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\layouts\\\\Play.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport Header from './Header';\nimport { usePlayer } from '../context/playerContext';\n// import { deletePath, addPlayerToRoom, listenToRules,listenToPlayers, listenToSpectatorJoin, listenToScores, listenToAnswers, listenToTimeStart, listenToBroadcastedAnswer, setupOnDisconnect, listenToRoundStart, listenToRoundRules } from '../services/firebaseServices';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport { useHost } from '../context/hostContext';\nimport RulesModal from '../components/RulesModal';\nimport { useFirebaseListener } from '../shared/hooks';\nimport { useTimeStart } from '../context/timeListenerContext';\nimport '../index.css';\nimport { useAppDispatch } from '../app/store';\nimport { setCurrentCorrectAnswer, clearPlayerAnswerList } from '../app/store/slices/gameSlice';\nimport { useSounds } from '../context/soundContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Play = ({\n  questionComponent,\n  isHost = false,\n  PlayerScore,\n  SideBar\n}) => {\n  _s();\n  var _roundTabs$find;\n  const roundTabs = [{\n    key: \"1\",\n    label: \"NHỔ NEO\"\n  }, {\n    key: \"2\",\n    label: \"VƯỢT SÓNG\"\n  }, {\n    key: \"3\",\n    label: \"BỨT PHÁ\"\n  }, {\n    key: \"4\",\n    label: \"CHINH PHỤC\"\n  }, {\n    key: \"summary\",\n    label: \"Tổng kết điểm\"\n  }, {\n    key: \"turn\",\n    label: \"Phân lượt\"\n  }];\n  const roundTime = {\n    \"1\": 10,\n    \"2\": 15,\n    \"3\": 60,\n    \"4\": 15,\n    \"turn\": 10\n  };\n  const navigate = useNavigate();\n  const playerAnswerRef = useRef(\"\");\n  const sounds = useSounds();\n  const [isChatOpen, setIsChatOpen] = useState(false);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [spectatorCount, setSpectatorCount] = useState(0);\n  const [showRulesModal, setShowRulesModal] = useState(false);\n  const [rulesRound, setRulesRound] = useState(\"1\");\n  const [userId, setUserId] = useState(localStorage.getItem(\"userId\"));\n  const [params] = useSearchParams();\n  const round = params.get(\"round\") || \"1\";\n  const {\n    players,\n    setPlayers,\n    setRoomId,\n    playersArray,\n    roomRules,\n    setRoomRules,\n    setPlayerArray,\n    position,\n    setCurrentQuestion,\n    selectedTopic,\n    setSelectedTopic,\n    setScoreList,\n    setAnswerList\n  } = usePlayer();\n  const {\n    playerScores,\n    setPlayerScores,\n    animationKey,\n    setAnimationKey,\n    mode,\n    rules\n  } = useHost();\n  const isMounted = useRef(false);\n  const {\n    timeLeft,\n    startTimer\n  } = useTimeStart();\n  const [searchParams] = useSearchParams();\n  const currentRound = searchParams.get(\"round\") || \"1\";\n  const testName = searchParams.get(\"testName\") || \"1\";\n  const roomId = searchParams.get(\"roomId\") || \"\";\n  const {\n    listenToCurrentQuestion,\n    listenToCorrectAnswer,\n    listenToNewPlayer,\n    setupDisconnect\n  } = useFirebaseListener(roomId);\n  const dispatch = useAppDispatch();\n  const isInitialMount = useRef(true);\n  const styles = `\n  @keyframes shrink {\n    from {\n      width: 100%;\n    }\n    to {\n      width: 0%;\n    }\n  }\n`;\n  // useEffect(() => {\n  //     if (isInitialMount.current) {\n  //         isInitialMount.current = false; // Allow subsequent runs\n  //         return;\n  //     }\n  //     setAnimationKey((prev) => prev + 1);\n  //     if (round === \"1\") {\n  //         console.log(\"start timer for round 1\");\n  //         startTimer(10);\n\n  //     }\n  //     if (round === \"2\") {\n  //         console.log(\"start timer for round 2\");\n  //         startTimer(15);\n  //     }\n  //     if (round === \"3\") {\n  //         console.log(\"start timer for round 3\");\n  //         startTimer(60);\n  //     }\n  //     if (round === \"4\") {\n  //         console.log(\"start timer for round 4\");\n  //         startTimer(15);\n  //     }\n  // }, [round]);\n\n  useEffect(() => {\n    const unsubscribePlayers = listenToNewPlayer(() => {});\n    return () => {\n      unsubscribePlayers();\n    };\n  }, []);\n  useEffect(() => {\n    const unsubscribeQuestion = listenToCurrentQuestion(() => {\n      if (!isHost) {\n        dispatch(setCurrentCorrectAnswer(\"\"));\n      }\n      dispatch(clearPlayerAnswerList());\n    });\n    return () => {\n      unsubscribeQuestion();\n    };\n  }, []);\n  useEffect(() => {\n    const unsubscribeAnswer = listenToCorrectAnswer(() => {\n      const audio = sounds['correct'];\n      if (audio) {\n        audio.play();\n      }\n    });\n    return () => {\n      unsubscribeAnswer();\n    };\n  }, []);\n\n  // const handleRoundChange = async (delta: number) => {\n  //     console.log(\"currentRound\", currentRound)\n  //     const newRound = parseInt(currentRound) + delta;\n  //     console.log(\"new round\", newRound)\n  //     if (newRound >= 1 && newRound <= 4) { // limit to 1-4 rounds\n  //         navigate(`?round=${newRound}&testName=${testName}&roomId=${roomId}`);\n  //     }\n\n  //     // Clear frontend state\n  //     setAnswerList([]);\n\n  //     // Clear Firebase data\n  //     await deletePath(roomId, \"questions\");\n  //     await deletePath(roomId, \"answers\");\n  //     await deletePath(roomId, \"answerLists\"); // Clear answer lists\n  //     await deletePath(roomId, \"turn\"); // Clear turn assignments\n  //     await deletePath(roomId, \"isModified\"); // Clear isModified state\n  // };\n\n  useEffect(() => {\n    if (!roomId || !userId) return;\n\n    // Setup onDisconnect to remove user from room when connection lost\n    const currentPlayer = JSON.parse(localStorage.getItem(\"currentPlayer\") || \"{}\");\n    const cancelOnDisconnect = setupDisconnect(roomId, userId, currentPlayer);\n    return () => {\n      // Optional: cancel onDisconnect if component unmounts normally\n      cancelOnDisconnect();\n    };\n  }, [roomId, userId]);\n  useEffect(() => {\n    const unsubscribeSpectator = listenToSpectatorJoin(roomId, count => {\n      console.log(\"spectator\", count);\n      setSpectatorCount(count);\n    });\n    return () => {\n      unsubscribeSpectator();\n    };\n  }, []);\n\n  // useEffect(() => {\n  //     const unsubscribeRules = listenToRoundRules(roomId, (data) => {\n  //         console.log(\"Rules data received:\", data);\n  //         setRoomRules(data)\n\n  //         // Show modal when host triggers it, regardless of round matching\n  //         if (data && data.show) {\n  //             setRulesRound(data.round);\n  //             setShowRulesModal(true);\n  //         } else {\n  //             setShowRulesModal(false);\n  //         }\n  //     })\n\n  //     return () => {\n  //         unsubscribeRules()\n  //     }\n  // }, [roomId])\n\n  // Remove auto-clear logic to allow proper modal synchronization between clients\n\n  const isFull = useRef(false);\n  // useEffect(() => {\n  //     const unsubscribePlayers = listenToPlayers(roomId, (updatedPlayers) => {\n  //         console.log(\"updatedPlayers\", updatedPlayers)\n  //         console.log(\"Object.keys(updatedPlayers)\", Object.keys(updatedPlayers))\n  //         console.log(\"Object.keys(updatedPlayers).length\", Object.keys(updatedPlayers).length)\n  //         if (updatedPlayers && Object.keys(updatedPlayers).length > 0) {\n  //             const playersList = Object.values(updatedPlayers);\n  //             console.log(\"playersList\", playersList);\n\n  //             const initialScoreList = [...playersList]\n  //             const scoreInitKey = `scoreInit_${roomId}_round1`;\n  //             const isFull = localStorage.getItem(`is_${roomId}_full`)\n\n  //             if (isFull != \"true\") {\n  //                 console.log(\"isFull inside\", isFull);\n\n  //                 console.log(\"initialScoreList\", initialScoreList);\n\n  //                 for (var score of initialScoreList) {\n  //                     score[\"score\"] = \"0\";\n  //                     score[\"isCorrect\"] = false;\n  //                     score[\"isModified\"] = false\n  //                 }\n  //                 console.log(\"initialScoreList\", initialScoreList);\n  //                 setScoreList(initialScoreList)\n  //                 setPlayerScores(initialScoreList)\n\n  //                 if (initialScoreList.length == 2) {\n  //                     localStorage.setItem(`is_${roomId}_full`, \"true\")\n  //                 }\n  //             }\n\n  //             // const currentPlayer = playersList.find((player: any) => player.uid === userId);\n  //             // const now = new Date().getTime();\n  //             // if(currentPlayer.lastActive && (now - currentPlayer.lastActive) > 10) {\n  //             //     return;\n  //             // }\n\n  //             setPlayerArray(playersList);\n  //             localStorage.setItem(\"playerList\", JSON.stringify(playersList));\n  //             console.log(\"Updated localStorage:\", localStorage.getItem(\"playerList\"));\n  //         } else {\n  //             console.log(\"Room is empty or players node deleted\");\n  //             console.log(\"roomId\", roomId);\n\n  //             setPlayerArray([]); // Clear state\n  //             localStorage.removeItem(\"playerList\"); // Clear localStorage\n  //         }\n  //     });\n\n  //     // No need to set state here; it's handled by useState initializer\n  //     return () => {\n  //         unsubscribePlayers();\n  //     };\n  // }, []);\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative \",\n    style: {\n      zoom: \"0.75\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-gradient-to-b from-slate-900 via-blue-900 to-blue-600\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,255,255,0.3)_1px,transparent_1px),radial-gradient(circle_at_75%_75%,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[length:100px_100px]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-blue-500/50 to-transparent\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 flex flex-col min-h-full\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        isHost: isHost,\n        spectatorCount: spectatorCount\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-1 p-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full lg:w-4/5 flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full h-3 bg-slate-700/50 rounded-full mb-4 border border-blue-400/30 shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-full bg-gradient-to-r from-blue-400 to-cyan-300 rounded-full shadow-inner\",\n              style: {\n                width: timeLeft > 0 ? '100%' : '100%',\n                // Always reset to 100% width\n                animation: timeLeft > 0 ? `shrink ${roundTime[round]}s linear forwards` : 'none',\n                animationPlayState: timeLeft > 0 ? 'running' : 'paused'\n              }\n            }, animationKey, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4  ${isHost ? \"min-h-[400px]\" : \"min-h-[400px]\"}`,\n            children: questionComponent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-slate-800/70 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-xl\",\n            children: PlayerScore\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:flex lg:w-1/5 flex-col gap-4\",\n          children: [!isHost && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 to-cyan-500 text-white text-center font-bold text-lg p-4 rounded-xl shadow-xl border border-blue-400/50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm opacity-90 mb-1\",\n              children: \"V\\xF2ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl\",\n              children: ((_roundTabs$find = roundTabs.find(tab => tab.key === currentRound)) === null || _roundTabs$find === void 0 ? void 0 : _roundTabs$find.label) || \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-slate-800/70 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-xl p-4 flex-1\",\n            children: SideBar\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"fixed bottom-6 right-6 bg-gradient-to-r from-blue-600 to-cyan-500 text-white w-14 h-14 flex items-center justify-center rounded-full shadow-2xl border-2 border-blue-400/50 hover:scale-110 transition-transform duration-200\",\n        onClick: () => setIsChatOpen(!isChatOpen),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xl\",\n          children: isChatOpen ? \"✖\" : \"💬\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 17\n      }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-slate-900/90 backdrop-blur-sm flex justify-center items-center z-50\",\n        onClick: () => setIsModalOpen(false),\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://a.travel-assets.com/findyours-php/viewfinder/images/res70/474000/474240-Left-Bank-Paris.jpg\",\n          alt: \"Full Size\",\n          className: \"max-w-full max-h-full rounded-xl shadow-2xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(RulesModal, {\n      isOpen: showRulesModal,\n      onClose: () => {\n        setShowRulesModal(false);\n        // Don't clear Firebase data - let each client manage their own modal state\n      },\n      round: rulesRound,\n      mode: mode,\n      roomRules: rules\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 294,\n    columnNumber: 9\n  }, this);\n};\n_s(Play, \"8wNO0vduVQjckgNZ6zoh1pBWT54=\", false, function () {\n  return [useNavigate, useSounds, useSearchParams, usePlayer, useHost, useTimeStart, useSearchParams, useFirebaseListener, useAppDispatch];\n});\n_c = Play;\nexport default Play;\nvar _c;\n$RefreshReg$(_c, \"Play\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Header", "usePlayer", "useNavigate", "useSearchParams", "useHost", "RulesModal", "useFirebaseListener", "useTimeStart", "useAppDispatch", "setCurrentCorrectAnswer", "clearPlayerAnswerList", "useSounds", "jsxDEV", "_jsxDEV", "Play", "questionComponent", "isHost", "PlayerScore", "SideBar", "_s", "_roundTabs$find", "roundTabs", "key", "label", "roundTime", "navigate", "playerAnswerRef", "sounds", "isChatOpen", "setIsChatOpen", "isModalOpen", "setIsModalOpen", "spectatorCount", "setSpectatorCount", "showRulesModal", "setShowRulesModal", "rulesRound", "setRulesRound", "userId", "setUserId", "localStorage", "getItem", "params", "round", "get", "players", "setPlayers", "setRoomId", "players<PERSON><PERSON>y", "roomRules", "setRoomRules", "setPlayerArray", "position", "setCurrentQuestion", "selectedTopic", "setSelectedTopic", "setScoreList", "setAnswerList", "playerScores", "setPlayerScores", "animationKey", "setAnimationKey", "mode", "rules", "isMounted", "timeLeft", "startTimer", "searchParams", "currentRound", "testName", "roomId", "listenToCurrentQuestion", "listenToCorrectAnswer", "listenToNewPlayer", "setupDisconnect", "dispatch", "isInitialMount", "styles", "unsubscribePlayers", "unsubscribeQuestion", "unsubscribeAnswer", "audio", "play", "currentPlayer", "JSON", "parse", "cancelOnDisconnect", "unsubscribeSpectator", "listenToSpectatorJoin", "count", "console", "log", "isFull", "className", "style", "zoom", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "animation", "animationPlayState", "find", "tab", "onClick", "src", "alt", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/layouts/Play.tsx"], "sourcesContent": ["import React, { useState, useEffect, ReactNode, useRef, useCallback } from 'react';\r\nimport Header from './Header';\r\nimport { usePlayer } from '../context/playerContext';\r\nimport { Answer, User } from '../type';\r\n// import { deletePath, addPlayerToRoom, listenToRules,listenToPlayers, listenToSpectatorJoin, listenToScores, listenToAnswers, listenToTimeStart, listenToBroadcastedAnswer, setupOnDisconnect, listenToRoundStart, listenToRoundRules } from '../services/firebaseServices';\r\nimport { useNavigate, useSearchParams } from 'react-router-dom';\r\nimport { submitAnswer } from './services';\r\nimport { getNextQuestion } from '../pages/Host/Test/service';\r\nimport { useHost } from '../context/hostContext';\r\nimport HostManagement from '../components/HostManagement';\r\nimport PlayerScore from '../components/PlayerScore';\r\nimport RulesModal from '../components/RulesModal';\r\nimport HostScore from '../components/PlayerAnswer';\r\nimport { setCurrentPacketQuestion } from '../components/services';\r\nimport { useFirebaseListener } from '../shared/hooks';\r\nimport { useTimeStart } from '../context/timeListenerContext';\r\nimport {\r\n    EyeIcon,\r\n} from \"@heroicons/react/24/solid\";\r\nimport '../index.css';\r\nimport { useAppDispatch, useAppSelector } from '../app/store';\r\nimport { setCurrentCorrectAnswer, clearPlayerAnswerList } from '../app/store/slices/gameSlice';\r\nimport { useSounds } from '../context/soundContext';\r\n\r\ninterface PlayProps {\r\n    questionComponent: ReactNode;\r\n    isHost?: boolean;\r\n    PlayerScore: ReactNode\r\n    SideBar: ReactNode\r\n}\r\n\r\ninterface Player {\r\n    score: number;\r\n    index: number;\r\n    username: string;\r\n    position: number;\r\n}\r\n\r\nconst Play: React.FC<PlayProps> = ({ questionComponent, isHost = false, PlayerScore, SideBar }) => {\r\n\r\n\r\n    const roundTabs = [\r\n        { key: \"1\", label: \"NHỔ NEO\" },\r\n        { key: \"2\", label: \"VƯỢT SÓNG\" },\r\n        { key: \"3\", label: \"BỨT PHÁ\" },\r\n        { key: \"4\", label: \"CHINH PHỤC\" },\r\n        { key: \"summary\", label: \"Tổng kết điểm\" },\r\n        { key: \"turn\", label: \"Phân lượt\" },\r\n    ];\r\n\r\n    const roundTime = {\r\n        \"1\": 10,\r\n        \"2\": 15,\r\n        \"3\": 60,\r\n        \"4\": 15,\r\n        \"turn\": 10,\r\n    }\r\n\r\n    const navigate = useNavigate()\r\n    const playerAnswerRef = useRef(\"\");\r\n    const sounds = useSounds();\r\n    const [isChatOpen, setIsChatOpen] = useState(false);\r\n    const [isModalOpen, setIsModalOpen] = useState(false);\r\n    const [spectatorCount, setSpectatorCount] = useState<number>(0)\r\n    const [showRulesModal, setShowRulesModal] = useState(false);\r\n    const [rulesRound, setRulesRound] = useState(\"1\");\r\n    const [userId, setUserId] = useState(localStorage.getItem(\"userId\"))\r\n    const [params] = useSearchParams()\r\n    const round = (params.get(\"round\") as \"1\" | \"2\" | \"3\" | \"4\" | \"turn\") || \"1\"\r\n    const { players, setPlayers, setRoomId, playersArray, roomRules, setRoomRules, setPlayerArray, position, setCurrentQuestion, selectedTopic, setSelectedTopic, setScoreList, setAnswerList } = usePlayer()\r\n    const { playerScores, setPlayerScores, animationKey, setAnimationKey, mode, rules } = useHost()\r\n    const isMounted = useRef(false);\r\n    const { timeLeft, startTimer } = useTimeStart();\r\n\r\n\r\n    const [searchParams] = useSearchParams();\r\n\r\n\r\n    const currentRound = searchParams.get(\"round\") || \"1\";\r\n    const testName = searchParams.get(\"testName\") || \"1\"\r\n    const roomId = searchParams.get(\"roomId\") || \"\";\r\n\r\n    const {\r\n        listenToCurrentQuestion, \r\n        listenToCorrectAnswer, \r\n        listenToNewPlayer,\r\n        \r\n        setupDisconnect\r\n    } = useFirebaseListener(roomId);\r\n    const dispatch = useAppDispatch();\r\n    const isInitialMount = useRef(true);\r\n    const styles = `\r\n  @keyframes shrink {\r\n    from {\r\n      width: 100%;\r\n    }\r\n    to {\r\n      width: 0%;\r\n    }\r\n  }\r\n`;\r\n    // useEffect(() => {\r\n    //     if (isInitialMount.current) {\r\n    //         isInitialMount.current = false; // Allow subsequent runs\r\n    //         return;\r\n    //     }\r\n    //     setAnimationKey((prev) => prev + 1);\r\n    //     if (round === \"1\") {\r\n    //         console.log(\"start timer for round 1\");\r\n    //         startTimer(10);\r\n\r\n    //     }\r\n    //     if (round === \"2\") {\r\n    //         console.log(\"start timer for round 2\");\r\n    //         startTimer(15);\r\n    //     }\r\n    //     if (round === \"3\") {\r\n    //         console.log(\"start timer for round 3\");\r\n    //         startTimer(60);\r\n    //     }\r\n    //     if (round === \"4\") {\r\n    //         console.log(\"start timer for round 4\");\r\n    //         startTimer(15);\r\n    //     }\r\n    // }, [round]);\r\n\r\n    useEffect(()=> {\r\n        const unsubscribePlayers = listenToNewPlayer(() => {\r\n\r\n        })\r\n\r\n        return () => {\r\n            unsubscribePlayers()\r\n        }\r\n    },[])\r\n\r\n    useEffect(() => {\r\n        const unsubscribeQuestion = listenToCurrentQuestion(() => {\r\n            if(!isHost) {\r\n                dispatch(setCurrentCorrectAnswer(\"\"))\r\n            }\r\n\r\n            dispatch(clearPlayerAnswerList())\r\n        })\r\n\r\n        return () => {\r\n            unsubscribeQuestion()\r\n        }\r\n    },[])\r\n\r\n    useEffect(() => {\r\n        const unsubscribeAnswer = listenToCorrectAnswer(\r\n            () => {\r\n                const audio = sounds['correct'];\r\n                if (audio) {\r\n                    audio.play();\r\n                }\r\n            }\r\n        );\r\n\r\n        return () => {\r\n            unsubscribeAnswer();\r\n        };\r\n    }, []);\r\n\r\n\r\n    // const handleRoundChange = async (delta: number) => {\r\n    //     console.log(\"currentRound\", currentRound)\r\n    //     const newRound = parseInt(currentRound) + delta;\r\n    //     console.log(\"new round\", newRound)\r\n    //     if (newRound >= 1 && newRound <= 4) { // limit to 1-4 rounds\r\n    //         navigate(`?round=${newRound}&testName=${testName}&roomId=${roomId}`);\r\n    //     }\r\n\r\n    //     // Clear frontend state\r\n    //     setAnswerList([]);\r\n\r\n    //     // Clear Firebase data\r\n    //     await deletePath(roomId, \"questions\");\r\n    //     await deletePath(roomId, \"answers\");\r\n    //     await deletePath(roomId, \"answerLists\"); // Clear answer lists\r\n    //     await deletePath(roomId, \"turn\"); // Clear turn assignments\r\n    //     await deletePath(roomId, \"isModified\"); // Clear isModified state\r\n    // };\r\n\r\n\r\n    useEffect(() => {\r\n        if (!roomId || !userId) return;\r\n\r\n        // Setup onDisconnect to remove user from room when connection lost\r\n        const currentPlayer = JSON.parse(localStorage.getItem(\"currentPlayer\") || \"{}\");\r\n        const cancelOnDisconnect = setupDisconnect(roomId, userId, currentPlayer,\r\n\r\n        );\r\n\r\n        return () => {\r\n            // Optional: cancel onDisconnect if component unmounts normally\r\n            cancelOnDisconnect();\r\n        };\r\n    }, [roomId, userId]);\r\n\r\n    useEffect(() => {\r\n        const unsubscribeSpectator = listenToSpectatorJoin(roomId, (count) => {\r\n            console.log(\"spectator\", count);\r\n            \r\n            setSpectatorCount(count)\r\n        })\r\n\r\n        return () => {\r\n            unsubscribeSpectator()\r\n        }\r\n    }, [])\r\n\r\n    // useEffect(() => {\r\n    //     const unsubscribeRules = listenToRoundRules(roomId, (data) => {\r\n    //         console.log(\"Rules data received:\", data);\r\n    //         setRoomRules(data)\r\n\r\n    //         // Show modal when host triggers it, regardless of round matching\r\n    //         if (data && data.show) {\r\n    //             setRulesRound(data.round);\r\n    //             setShowRulesModal(true);\r\n    //         } else {\r\n    //             setShowRulesModal(false);\r\n    //         }\r\n    //     })\r\n\r\n    //     return () => {\r\n    //         unsubscribeRules()\r\n    //     }\r\n    // }, [roomId])\r\n\r\n    // Remove auto-clear logic to allow proper modal synchronization between clients\r\n\r\n    const isFull = useRef(false)\r\n    // useEffect(() => {\r\n    //     const unsubscribePlayers = listenToPlayers(roomId, (updatedPlayers) => {\r\n    //         console.log(\"updatedPlayers\", updatedPlayers)\r\n    //         console.log(\"Object.keys(updatedPlayers)\", Object.keys(updatedPlayers))\r\n    //         console.log(\"Object.keys(updatedPlayers).length\", Object.keys(updatedPlayers).length)\r\n    //         if (updatedPlayers && Object.keys(updatedPlayers).length > 0) {\r\n    //             const playersList = Object.values(updatedPlayers);\r\n    //             console.log(\"playersList\", playersList);\r\n\r\n    //             const initialScoreList = [...playersList]\r\n    //             const scoreInitKey = `scoreInit_${roomId}_round1`;\r\n    //             const isFull = localStorage.getItem(`is_${roomId}_full`)\r\n\r\n    //             if (isFull != \"true\") {\r\n    //                 console.log(\"isFull inside\", isFull);\r\n\r\n    //                 console.log(\"initialScoreList\", initialScoreList);\r\n\r\n    //                 for (var score of initialScoreList) {\r\n    //                     score[\"score\"] = \"0\";\r\n    //                     score[\"isCorrect\"] = false;\r\n    //                     score[\"isModified\"] = false\r\n    //                 }\r\n    //                 console.log(\"initialScoreList\", initialScoreList);\r\n    //                 setScoreList(initialScoreList)\r\n    //                 setPlayerScores(initialScoreList)\r\n\r\n    //                 if (initialScoreList.length == 2) {\r\n    //                     localStorage.setItem(`is_${roomId}_full`, \"true\")\r\n    //                 }\r\n    //             }\r\n\r\n    //             // const currentPlayer = playersList.find((player: any) => player.uid === userId);\r\n    //             // const now = new Date().getTime();\r\n    //             // if(currentPlayer.lastActive && (now - currentPlayer.lastActive) > 10) {\r\n    //             //     return;\r\n    //             // }\r\n\r\n    //             setPlayerArray(playersList);\r\n    //             localStorage.setItem(\"playerList\", JSON.stringify(playersList));\r\n    //             console.log(\"Updated localStorage:\", localStorage.getItem(\"playerList\"));\r\n    //         } else {\r\n    //             console.log(\"Room is empty or players node deleted\");\r\n    //             console.log(\"roomId\", roomId);\r\n\r\n    //             setPlayerArray([]); // Clear state\r\n    //             localStorage.removeItem(\"playerList\"); // Clear localStorage\r\n    //         }\r\n    //     });\r\n\r\n    //     // No need to set state here; it's handled by useState initializer\r\n    //     return () => {\r\n    //         unsubscribePlayers();\r\n    //     };\r\n    // }, []);\r\n\r\n\r\n    return (\r\n        <div className=\"relative \"\r\n            style={{ zoom: \"0.75\" }}\r\n        >\r\n            {/* Ocean/Starry Night Background */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-b from-slate-900 via-blue-900 to-blue-600\">\r\n                {/* Stars overlay */}\r\n                <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,255,255,0.3)_1px,transparent_1px),radial-gradient(circle_at_75%_75%,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[length:100px_100px]\"></div>\r\n                {/* Ocean waves effect */}\r\n                <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-blue-500/50 to-transparent\"></div>\r\n                {/* Subtle animated waves */}\r\n                <div className=\"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-pulse\"></div>\r\n            </div>\r\n\r\n            {/* Content overlay */}\r\n            <div className=\"relative z-10 flex flex-col min-h-full\">\r\n                <Header isHost={isHost} spectatorCount={spectatorCount} />\r\n\r\n                <div className=\"flex flex-1 p-4 gap-4\">\r\n                    <div className=\"w-full lg:w-4/5 flex flex-col\">\r\n                        {/* Progress bar with ocean theme */}\r\n                        <div className=\"w-full h-3 bg-slate-700/50 rounded-full mb-4 border border-blue-400/30 shadow-lg\">\r\n                            <div\r\n                                className=\"h-full bg-gradient-to-r from-blue-400 to-cyan-300 rounded-full shadow-inner\"\r\n                                style={{\r\n                                    width: timeLeft > 0 ? '100%' : '100%', // Always reset to 100% width\r\n                                    animation: timeLeft > 0 ? `shrink ${roundTime[round]}s linear forwards` : 'none',\r\n                                    animationPlayState: timeLeft > 0 ? 'running' : 'paused',\r\n                                }}\r\n                                key={animationKey} // Restart animation on round change\r\n                            ></div>\r\n                        </div>\r\n\r\n                        {/* Question component with ocean-themed styling */}\r\n                        <div className={`bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4  ${isHost ? \"min-h-[400px]\" : \"min-h-[400px]\"}`}>\r\n                            {questionComponent}\r\n                        </div>\r\n\r\n                        {/* Player score with ocean theme */}\r\n                        <div className=\"bg-slate-800/70 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-xl\">\r\n                            {PlayerScore}\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"hidden lg:flex lg:w-1/5 flex-col gap-4\">\r\n                        {/* Round indicator with nautical theme */}\r\n                        {!isHost && (\r\n                            <div className=\"bg-gradient-to-r from-blue-600 to-cyan-500 text-white text-center font-bold text-lg p-4 rounded-xl shadow-xl border border-blue-400/50\">\r\n                                <div className=\"text-sm opacity-90 mb-1\">Vòng</div>\r\n                                <div className=\"text-xl\">\r\n                                    {roundTabs.find(tab => tab.key === currentRound)?.label || \"\"}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n                        {/* Sidebar with ocean theme */}\r\n                        <div className=\"bg-slate-800/70 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-xl p-4 flex-1\">\r\n                            {SideBar}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Mobile round indicator */}\r\n                {/* <div className=\"lg:hidden mx-4 mb-4\">\r\n                    <div className=\"bg-gradient-to-r from-blue-600 to-cyan-500 text-white text-center font-bold text-base p-3 rounded-xl shadow-xl border border-blue-400/50\">\r\n                        <span className=\"text-sm opacity-90\">Vòng </span>\r\n                        <span>{round ? roundName[round as keyof typeof roundName] : \"\"}</span>\r\n                    </div>\r\n                </div> */}\r\n\r\n                {/* Chat button with ocean theme */}\r\n                <button\r\n                    className=\"fixed bottom-6 right-6 bg-gradient-to-r from-blue-600 to-cyan-500 text-white w-14 h-14 flex items-center justify-center rounded-full shadow-2xl border-2 border-blue-400/50 hover:scale-110 transition-transform duration-200\"\r\n                    onClick={() => setIsChatOpen(!isChatOpen)}\r\n                >\r\n                    <span className=\"text-xl\">{isChatOpen ? \"✖\" : \"💬\"}</span>\r\n                </button>\r\n\r\n                {/* Modal with ocean theme */}\r\n                {isModalOpen && (\r\n                    <div\r\n                        className=\"fixed inset-0 bg-slate-900/90 backdrop-blur-sm flex justify-center items-center z-50\"\r\n                        onClick={() => setIsModalOpen(false)}\r\n                    >\r\n                        <img\r\n                            src=\"https://a.travel-assets.com/findyours-php/viewfinder/images/res70/474000/474240-Left-Bank-Paris.jpg\"\r\n                            alt=\"Full Size\"\r\n                            className=\"max-w-full max-h-full rounded-xl shadow-2xl\"\r\n                        />\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Rules Modal */}\r\n            <RulesModal\r\n                isOpen={showRulesModal}\r\n                onClose={() => {\r\n                    setShowRulesModal(false);\r\n                    // Don't clear Firebase data - let each client manage their own modal state\r\n                }}\r\n                round={rulesRound}\r\n                mode={mode}\r\n                roomRules={rules}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\n\r\n\r\nexport default Play;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAaC,MAAM,QAAqB,OAAO;AAClF,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,SAAS,QAAQ,0BAA0B;AAEpD;AACA,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAG/D,SAASC,OAAO,QAAQ,wBAAwB;AAGhD,OAAOC,UAAU,MAAM,0BAA0B;AAGjD,SAASC,mBAAmB,QAAQ,iBAAiB;AACrD,SAASC,YAAY,QAAQ,gCAAgC;AAI7D,OAAO,cAAc;AACrB,SAASC,cAAc,QAAwB,cAAc;AAC7D,SAASC,uBAAuB,EAAEC,qBAAqB,QAAQ,+BAA+B;AAC9F,SAASC,SAAS,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgBpD,MAAMC,IAAyB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC,MAAM,GAAG,KAAK;EAAEC,WAAW;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA;EAG/F,MAAMC,SAAS,GAAG,CACd;IAAEC,GAAG,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9B;IAAED,GAAG,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAY,CAAC,EAChC;IAAED,GAAG,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9B;IAAED,GAAG,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAa,CAAC,EACjC;IAAED,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC1C;IAAED,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAY,CAAC,CACtC;EAED,MAAMC,SAAS,GAAG;IACd,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,MAAM,EAAE;EACZ,CAAC;EAED,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,eAAe,GAAG3B,MAAM,CAAC,EAAE,CAAC;EAClC,MAAM4B,MAAM,GAAGhB,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAS,CAAC,CAAC;EAC/D,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,GAAG,CAAC;EACjD,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC2C,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;EACpE,MAAM,CAACC,MAAM,CAAC,GAAGvC,eAAe,CAAC,CAAC;EAClC,MAAMwC,KAAK,GAAID,MAAM,CAACE,GAAG,CAAC,OAAO,CAAC,IAAuC,GAAG;EAC5E,MAAM;IAAEC,OAAO;IAAEC,UAAU;IAAEC,SAAS;IAAEC,YAAY;IAAEC,SAAS;IAAEC,YAAY;IAAEC,cAAc;IAAEC,QAAQ;IAAEC,kBAAkB;IAAEC,aAAa;IAAEC,gBAAgB;IAAEC,YAAY;IAAEC;EAAc,CAAC,GAAGxD,SAAS,CAAC,CAAC;EACzM,MAAM;IAAEyD,YAAY;IAAEC,eAAe;IAAEC,YAAY;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAG3D,OAAO,CAAC,CAAC;EAC/F,MAAM4D,SAAS,GAAGjE,MAAM,CAAC,KAAK,CAAC;EAC/B,MAAM;IAAEkE,QAAQ;IAAEC;EAAW,CAAC,GAAG3D,YAAY,CAAC,CAAC;EAG/C,MAAM,CAAC4D,YAAY,CAAC,GAAGhE,eAAe,CAAC,CAAC;EAGxC,MAAMiE,YAAY,GAAGD,YAAY,CAACvB,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EACrD,MAAMyB,QAAQ,GAAGF,YAAY,CAACvB,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG;EACpD,MAAM0B,MAAM,GAAGH,YAAY,CAACvB,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;EAE/C,MAAM;IACF2B,uBAAuB;IACvBC,qBAAqB;IACrBC,iBAAiB;IAEjBC;EACJ,CAAC,GAAGpE,mBAAmB,CAACgE,MAAM,CAAC;EAC/B,MAAMK,QAAQ,GAAGnE,cAAc,CAAC,CAAC;EACjC,MAAMoE,cAAc,GAAG7E,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM8E,MAAM,GAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;EACG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA/E,SAAS,CAAC,MAAK;IACX,MAAMgF,kBAAkB,GAAGL,iBAAiB,CAAC,MAAM,CAEnD,CAAC,CAAC;IAEF,OAAO,MAAM;MACTK,kBAAkB,CAAC,CAAC;IACxB,CAAC;EACL,CAAC,EAAC,EAAE,CAAC;EAELhF,SAAS,CAAC,MAAM;IACZ,MAAMiF,mBAAmB,GAAGR,uBAAuB,CAAC,MAAM;MACtD,IAAG,CAACvD,MAAM,EAAE;QACR2D,QAAQ,CAAClE,uBAAuB,CAAC,EAAE,CAAC,CAAC;MACzC;MAEAkE,QAAQ,CAACjE,qBAAqB,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,OAAO,MAAM;MACTqE,mBAAmB,CAAC,CAAC;IACzB,CAAC;EACL,CAAC,EAAC,EAAE,CAAC;EAELjF,SAAS,CAAC,MAAM;IACZ,MAAMkF,iBAAiB,GAAGR,qBAAqB,CAC3C,MAAM;MACF,MAAMS,KAAK,GAAGtD,MAAM,CAAC,SAAS,CAAC;MAC/B,IAAIsD,KAAK,EAAE;QACPA,KAAK,CAACC,IAAI,CAAC,CAAC;MAChB;IACJ,CACJ,CAAC;IAED,OAAO,MAAM;MACTF,iBAAiB,CAAC,CAAC;IACvB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAGN;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAGAlF,SAAS,CAAC,MAAM;IACZ,IAAI,CAACwE,MAAM,IAAI,CAAChC,MAAM,EAAE;;IAExB;IACA,MAAM6C,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC7C,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC;IAC/E,MAAM6C,kBAAkB,GAAGZ,eAAe,CAACJ,MAAM,EAAEhC,MAAM,EAAE6C,aAE3D,CAAC;IAED,OAAO,MAAM;MACT;MACAG,kBAAkB,CAAC,CAAC;IACxB,CAAC;EACL,CAAC,EAAE,CAAChB,MAAM,EAAEhC,MAAM,CAAC,CAAC;EAEpBxC,SAAS,CAAC,MAAM;IACZ,MAAMyF,oBAAoB,GAAGC,qBAAqB,CAAClB,MAAM,EAAGmB,KAAK,IAAK;MAClEC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,KAAK,CAAC;MAE/BxD,iBAAiB,CAACwD,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEF,OAAO,MAAM;MACTF,oBAAoB,CAAC,CAAC;IAC1B,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;;EAEA,MAAMK,MAAM,GAAG7F,MAAM,CAAC,KAAK,CAAC;EAC5B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAGA,oBACIc,OAAA;IAAKgF,SAAS,EAAC,WAAW;IACtBC,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAGxBnF,OAAA;MAAKgF,SAAS,EAAC,2EAA2E;MAAAG,QAAA,gBAEtFnF,OAAA;QAAKgF,SAAS,EAAC;MAAyM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE/NvF,OAAA;QAAKgF,SAAS,EAAC;MAAwF;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE9GvF,OAAA;QAAKgF,SAAS,EAAC;MAAsH;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3I,CAAC,eAGNvF,OAAA;MAAKgF,SAAS,EAAC,wCAAwC;MAAAG,QAAA,gBACnDnF,OAAA,CAACb,MAAM;QAACgB,MAAM,EAAEA,MAAO;QAACgB,cAAc,EAAEA;MAAe;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1DvF,OAAA;QAAKgF,SAAS,EAAC,uBAAuB;QAAAG,QAAA,gBAClCnF,OAAA;UAAKgF,SAAS,EAAC,+BAA+B;UAAAG,QAAA,gBAE1CnF,OAAA;YAAKgF,SAAS,EAAC,kFAAkF;YAAAG,QAAA,eAC7FnF,OAAA;cACIgF,SAAS,EAAC,6EAA6E;cACvFC,KAAK,EAAE;gBACHO,KAAK,EAAEpC,QAAQ,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;gBAAE;gBACvCqC,SAAS,EAAErC,QAAQ,GAAG,CAAC,GAAG,UAAUzC,SAAS,CAACmB,KAAK,CAAC,mBAAmB,GAAG,MAAM;gBAChF4D,kBAAkB,EAAEtC,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG;cACnD;YAAE,GACGL,YAAY;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNvF,OAAA;YAAKgF,SAAS,EAAE,8FAA8F7E,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;YAAAgF,QAAA,EACtJjF;UAAiB;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eAGNvF,OAAA;YAAKgF,SAAS,EAAC,iFAAiF;YAAAG,QAAA,EAC3F/E;UAAW;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENvF,OAAA;UAAKgF,SAAS,EAAC,wCAAwC;UAAAG,QAAA,GAElD,CAAChF,MAAM,iBACJH,OAAA;YAAKgF,SAAS,EAAC,wIAAwI;YAAAG,QAAA,gBACnJnF,OAAA;cAAKgF,SAAS,EAAC,yBAAyB;cAAAG,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnDvF,OAAA;cAAKgF,SAAS,EAAC,SAAS;cAAAG,QAAA,EACnB,EAAA5E,eAAA,GAAAC,SAAS,CAACmF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnF,GAAG,KAAK8C,YAAY,CAAC,cAAAhD,eAAA,uBAA/CA,eAAA,CAAiDG,KAAK,KAAI;YAAE;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAEDvF,OAAA;YAAKgF,SAAS,EAAC,4FAA4F;YAAAG,QAAA,EACtG9E;UAAO;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAWNvF,OAAA;QACIgF,SAAS,EAAC,+NAA+N;QACzOa,OAAO,EAAEA,CAAA,KAAM7E,aAAa,CAAC,CAACD,UAAU,CAAE;QAAAoE,QAAA,eAE1CnF,OAAA;UAAMgF,SAAS,EAAC,SAAS;UAAAG,QAAA,EAAEpE,UAAU,GAAG,GAAG,GAAG;QAAI;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,EAGRtE,WAAW,iBACRjB,OAAA;QACIgF,SAAS,EAAC,sFAAsF;QAChGa,OAAO,EAAEA,CAAA,KAAM3E,cAAc,CAAC,KAAK,CAAE;QAAAiE,QAAA,eAErCnF,OAAA;UACI8F,GAAG,EAAC,qGAAqG;UACzGC,GAAG,EAAC,WAAW;UACff,SAAS,EAAC;QAA6C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGNvF,OAAA,CAACR,UAAU;MACPwG,MAAM,EAAE3E,cAAe;MACvB4E,OAAO,EAAEA,CAAA,KAAM;QACX3E,iBAAiB,CAAC,KAAK,CAAC;QACxB;MACJ,CAAE;MACFQ,KAAK,EAAEP,UAAW;MAClB0B,IAAI,EAAEA,IAAK;MACXb,SAAS,EAAEc;IAAM;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACjF,EAAA,CAvWIL,IAAyB;EAAA,QAoBVZ,WAAW,EAEbS,SAAS,EAOPR,eAAe,EAE8JF,SAAS,EACjHG,OAAO,EAE5DG,YAAY,EAGtBJ,eAAe,EAalCG,mBAAmB,EACNE,cAAc;AAAA;AAAAuG,EAAA,GAnD7BjG,IAAyB;AA2W/B,eAAeA,IAAI;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}