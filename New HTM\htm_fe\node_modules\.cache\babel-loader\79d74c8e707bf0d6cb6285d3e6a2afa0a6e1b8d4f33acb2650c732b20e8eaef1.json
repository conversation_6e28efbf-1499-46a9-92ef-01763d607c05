{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\layouts\\\\Host\\\\Host.tsx\";\nimport React from 'react';\nimport Play from '../Play';\nimport HostAnswer from '../../components/HostAnswer';\nimport HostManagement from '../../components/HostManagement';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Host = ({\n  QuestionComponent\n}) => {\n  return /*#__PURE__*/_jsxDEV(Play, {\n    questionComponent: QuestionComponent,\n    PlayerScore: /*#__PURE__*/_jsxDEV(HostAnswer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 22\n    }, this),\n    SideBar: /*#__PURE__*/_jsxDEV(HostManagement, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 18\n    }, this),\n    isHost: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = Host;\nexport default Host;\nvar _c;\n$RefreshReg$(_c, \"Host\");", "map": {"version": 3, "names": ["React", "Play", "HostAnswer", "HostManagement", "jsxDEV", "_jsxDEV", "Host", "QuestionComponent", "questionComponent", "PlayerScore", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "SideBar", "isHost", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/layouts/Host/Host.tsx"], "sourcesContent": ["import React from 'react'\r\nimport Play from '../Play'\r\nimport HostAnswer from '../../components/HostAnswer'\r\nimport HostManagement from '../../components/HostManagement'\r\n\r\ninterface HostInterfaceProps {\r\n    QuestionComponent: React.ReactNode\r\n}\r\n\r\nconst Host:React.FC<HostInterfaceProps> = ({QuestionComponent}) => {\r\n  return (\r\n    <Play\r\n        questionComponent={QuestionComponent}\r\n        PlayerScore={<HostAnswer/>}\r\n        SideBar={<HostManagement/>}\r\n        isHost={true}\r\n    />\r\n  )\r\n}\r\n\r\nexport default Host"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,cAAc,MAAM,iCAAiC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAM5D,MAAMC,IAAiC,GAAGA,CAAC;EAACC;AAAiB,CAAC,KAAK;EACjE,oBACEF,OAAA,CAACJ,IAAI;IACDO,iBAAiB,EAAED,iBAAkB;IACrCE,WAAW,eAAEJ,OAAA,CAACH,UAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAE;IAC3BC,OAAO,eAAET,OAAA,CAACF,cAAc;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAE;IAC3BE,MAAM,EAAE;EAAK;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEN,CAAC;AAAAG,EAAA,GATKV,IAAiC;AAWvC,eAAeA,IAAI;AAAA,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}